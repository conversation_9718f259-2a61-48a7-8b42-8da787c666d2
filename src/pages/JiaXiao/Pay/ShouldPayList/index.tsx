import React, { useEffect, useRef, useState } from "react";
import { Button, message, Modal, Space } from "antd";
import { PageContainer } from "@ant-design/pro-components";
import { SearchParams, ShouldPayItem } from "./types";
import { convertSearchItemToFormItems, DEFAULT_SEARCH_ITEMS } from "./constants";
import request from "@/utils/request";
import JxStudent from "@/components/JiaXiao/Student";
import MyTable, { MyTableColumnProps } from "@/components/CustomComponent/MyTable/index";
import { FormItem } from "@/components/CustomComponent/DynamicFormBuilder/index";
import PageSearchItems from "@/components/CustomComponent/PageSearchItems/index";
import PayInfo from "@/components/JiaXiao/Student/module/PayInfo";
import PayFormModal from "./components/PayFormModal";
import { QuestionCircleOutlined } from "@ant-design/icons";
import confirmRequest from "@/utils/confirmRequest";

const ShouldPayList: React.FC = () => {
    const [messageApi, contextHolder] = message.useMessage();

    // Refs
    const tableRef = useRef<any>(null);
    const studentInfoRef = useRef<any>(null);
    const payInfoRef = useRef<any>(null);

    // State
    const [formCols, setFormCols] = useState<FormItem[]>([]);
    const [searchItems, setSearchItems] = useState<any>(DEFAULT_SEARCH_ITEMS);
    const [searchParams, setSearchParams] = useState<SearchParams>({});
    const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
    const [balance, setBalance] = useState<string>("0");
    const [showPayForm, setShowPayForm] = useState<boolean>(false);
    const [payFormInfo, setPayFormInfo] = useState<any>({});

    // Initialize form columns
    useEffect(() => {
        const cols = convertSearchItemToFormItems(searchItems);
        setFormCols(cols);
    }, []);

    // Handle search params change
    const handleSearchParamsChange = (params: any) => {
        setSearchParams(params);
    };

    // Get payment summary
    const getShouldPaySummary = async (params: any) => {
        try {
            const response = await request("/Jx/Pay/JxShouldPay/getShouldPaySummary", {
                method: "POST",
                data: params,
            });
            if (response.success) {
                setBalance(response.data.TotalMoney || "0");
            }
        } catch (error) {
            console.error("Failed to get payment summary:", error);
        }
    };

    // Handle payment
    const handlePayment = (record: ShouldPayItem) => {
        messageApi.loading({
            content: "获取付款信息",
            key: "loading",
            duration: 0,
        });

        request("/Jx/Pay/JxShouldPay/getPayInfo", {
            method: "POST",
            data: {
                Id: record.Id,
            },
        }).then((json) => {
            messageApi.destroy("loading");
            if (json && json.success) {
                setShowPayForm(true);
                setPayFormInfo(json.data);
            } else {
                messageApi.error(json.message || "获取付款信息失败");
            }
        });
    };

    // Handle delete
    const handleDelete = (id: string) => {
        Modal.confirm({
            title: "确认删除",
            icon: <QuestionCircleOutlined />,
            content: "确定要删除这条挂账记录吗？",
            okText: "确认",
            cancelText: "取消",
            onOk: async () => {
                try {
                    const response = await confirmRequest("/Jx/Pay/JxShouldPay/deleteJxShouldPay", {
                        method: "DELETE",
                        data: { Id: id },
                    });
                    if (response.success) {
                        messageApi.success("删除成功");
                        tableRef.current?.reload();
                    } else {
                        messageApi.error(response.message || "删除失败");
                    }
                } catch (error) {
                    messageApi.error("删除失败");
                }
            },
        });
    };

    // Table columns
    const columns: MyTableColumnProps[] = [
        {
            title: "序号",
            dataIndex: "RowIndex",
            width: 60,
            fixed: "left",
        },
        {
            title: "学员姓名",
            dataIndex: "StudentName",
            width: 100,
            fixed: "left",
            render: (text, record) => (
                <a onClick={() => studentInfoRef.current?.show(record.StudentId)}>{text}</a>
            ),
        },
        {
            title: "身份证号",
            dataIndex: "IdCard",
            width: 180,
        },
        {
            title: "手机号码",
            dataIndex: "Mobile",
            width: 120,
        },
        {
            title: "驾校部门",
            dataIndex: "JxDeptName",
            width: 120,
        },
        {
            title: "费用类型",
            dataIndex: "CostTypeName",
            width: 120,
        },
        {
            title: "挂账金额",
            dataIndex: "PayMoney",
            width: 100,
            render: (text) => <span style={{ color: "#f50" }}>{text}</span>,
        },
        {
            title: "挂账日期",
            dataIndex: "PayTime",
            width: 180,
        },
        {
            title: "状态",
            dataIndex: "StatusName",
            width: 100,
            render: (text, record) => {
                let color = "";
                if (record.Status === "0") color = "#f50";
                else if (record.Status === "1") color = "#1890ff";
                else if (record.Status === "2") color = "#52c41a";
                return <span style={{ color }}>{text}</span>;
            },
        },
        {
            title: "创建人",
            dataIndex: "CreateUserName",
            width: 100,
        },
        {
            title: "创建时间",
            dataIndex: "CreateTime",
            width: 180,
        },
        {
            title: "备注",
            dataIndex: "Remark",
            width: 200,
            ellipsis: true,
        },
        {
            title: "操作",
            dataIndex: "operation",
            fixed: "right",
            width: 120,
            render: (_, record) => (
                <Space size="small">
                    {record.Status !== "2" && (
                        <a onClick={() => handlePayment(record)}>缴费</a>
                    )}
                    <a onClick={() => handleDelete(record.Id)}>删除</a>
                </Space>
            ),
        },
    ];

    // Table buttons
    const tableButtons = [
        <Button
            key="add-should-pay"
            type="primary"
            onClick={() => {
                // Open add form
                payInfoRef.current?.show();
            }}
        >
            新增挂账
        </Button>,
    ];

    return (
        <PageContainer header={{ breadcrumb: {}, title: "" }}>
            {contextHolder}
            <MyTable
                key="should-pay-list-table"
                ref={tableRef}
                formCols={formCols}
                setSearchParams={handleSearchParamsChange}
                onLoadCallback={(params) => getShouldPaySummary(params)}
                tableButtons={tableButtons}
                formButtons={[
                    <PageSearchItems
                        pageName="JxShouldPayList"
                        key="ShouldPayList-PageSearchItems-1"
                        searchItems={searchItems}
                        onCallBack={(items: any) => {
                            setSearchItems(items);
                            const cols = convertSearchItemToFormItems(items);
                            setFormCols(cols);
                        }}
                    />,
                ]}
                columns={columns}
                rowKey="Id"
                getPath="/Jx/Pay/JxShouldPay/getShouldPayList"
                tableParams={searchParams}
                downloadPath="/Jx/Pay/JxShouldPay/exportShouldPayList"
                downloadTableName="JxShouldPayList"
                downloadSelectRowKeysName="Ids"
                rowSelection={{
                    selectedRowKeys,
                    setSelectedRowKeys,
                    getCheckboxProps: (record) => ({
                        disabled: false,
                    }),
                }}
                balance={balance !== "0" ? <span>挂账总金额：{balance}元</span> : null}
            />
            <JxStudent ref={studentInfoRef} StudentListRef={undefined} updateAddLoading={undefined} />
            <PayInfo ref={payInfoRef} ShouldPayListRef={tableRef} JxPayListRef={undefined} />
            <PayFormModal
                open={showPayForm}
                onOpenChange={setShowPayForm}
                payFormInfo={payFormInfo}
                onSuccess={() => {
                    tableRef.current?.reload();
                }}
                messageApi={messageApi}
            />
        </PageContainer>
    );
};

export default ShouldPayList;
