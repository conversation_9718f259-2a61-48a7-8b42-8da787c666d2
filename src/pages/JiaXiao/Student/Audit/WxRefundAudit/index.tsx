import MyTable, { MyTableColumnProps } from "@/components/CustomComponent/MyTable/index";
import { PageContainer } from "@ant-design/pro-components";
import { Button, Card, Space, Statistic } from "antd";
import { QrcodeOutlined, LinkOutlined } from "@ant-design/icons";
import React, { useRef, useState } from "react";
import { FormItem } from "@/components/CustomComponent/DynamicFormBuilder";
import ApplyLinkButton from "@/components/CustomComponent/MyTable/ApplyLinkButton";

const WxRefundAudit: React.FC = () => {
    const tableRef = useRef<any>();
    const [balance, setBalance] = useState<number>(0);

    const formItems: FormItem[] = [
        {
            name: "CreateTime",
            type: "datetimerange",
            label: "申请时间",
            placeholder: "请选择时间范围",
            required: false
        },
        {
            name: "AuditStatus",
            type: "select",
            label: "审核状态",
            placeholder: "请选择审核状态",
            required: false,
            options: [
                { label: "待审核", value: "Pending" },
                { label: "已通过", value: "Approved" },
                { label: "已拒绝", value: "Rejected" }
            ]
        },
        {
            name: "RefundStatus",
            type: "select",
            label: "退款状态",
            placeholder: "请选择退款状态",
            required: false,
            options: [
                { label: "未退款", value: "NotRefunded" },
                { label: "已退款", value: "Refunded" },
                { label: "退款中", value: "Refunding" }
            ]
        }
    ];

    const columns: MyTableColumnProps[] = [
        { dataIndex: "RowIndex", title: "序号", fixed: "left", align: "center" },
        { dataIndex: "StudentName", title: "学员姓名", align: "center" },
        { dataIndex: "IdCard", title: "身份证号", align: "center" },
        { dataIndex: "Phone", title: "手机号码", align: "center" },
        { dataIndex: "RefundAmount", title: "退款金额", align: "center" },
        { dataIndex: "RefundReason", title: "退款原因", align: "center" },
        { dataIndex: "Status", title: "状态", align: "center" },
        { dataIndex: "CreateTime", title: "申请时间", align: "center" },
        { dataIndex: "AuditTime", title: "审核时间", align: "center" },
        { dataIndex: "AuditUser", title: "审核人", align: "center" },
        {
            dataIndex: "Id",
            title: "操作",
            fixed: "right",
            align: "center",
            render: (_: any, record: any) => (
                <Space>
                    <a onClick={() => handleAudit(record)}>审核</a>
                    <Button
                        type="link"
                        icon={<QrcodeOutlined />}
                        onClick={() => handleGenerateQRCode(record)}
                    >
                        申请二维码
                    </Button>
                </Space>
            ),
        },
    ];

    const handleAudit = (record: any) => {
        // TODO: Implement audit logic
        console.log('Audit record:', record);
    };

    const handleGenerateQRCode = (record: any) => {
        // TODO: Implement QR code generation logic
        console.log('Generate QR code for record:', record);
    };

    // Define table buttons
    const tableButtons = [
        <ApplyLinkButton key="apply-link" tableName="WxRefundAudit" />
    ];

    return (
        <PageContainer header={{ breadcrumb: {}, title: "" }}>
            <Card style={{ marginBottom: 16 }}>
                <Statistic
                    title="账户余额"
                    value={balance}
                    precision={2}
                    prefix="¥"
                />
            </Card>
            <MyTable
                key="wx-refund-audit-table"
                ref={tableRef}
                formCols={formItems}
                columns={columns}
                getPath="/JiaXiao/Student/Audit/WxRefundAudit/getList"
                rowKey="Id"
                downloadTableName="WxRefundAudit"
            />
        </PageContainer>
    );
};

export default WxRefundAudit;