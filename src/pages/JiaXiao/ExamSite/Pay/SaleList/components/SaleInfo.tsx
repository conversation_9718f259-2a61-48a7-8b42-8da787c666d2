import React, { useRef, useState } from "react";
import { But<PERSON>, message } from "antd";
import MyFormModal from "@/components/CustomComponent/MyFormModal/index";
import RefundSale from "./RefundSale";
import UseSale from "./UseSale";
import confirmRequest from "@/utils/confirmRequest";
import request from "@/utils/request";
import { PrintScan } from "@/services/print/scan";
import { PrintCoupon } from "@/services/print/coupon";

interface Props {
    Id: string;
    TenantId: string;
    children?: React.ReactNode;
    tableRef?: React.RefObject<any>;
}

const SaleInfo = React.forwardRef<any, Props>((props, ref) => {
    const formRef = useRef<any>();
    const refundRef = useRef<any>();
    const useSaleRef = useRef<any>(null);
    const [messageApi, contextHolder] = message.useMessage();
    const [activeTab, setActiveTab] = useState("tab1");
    const [resetPrintLoading, setResetPrintLoading] = useState(false);

    const handleTabChange = (key: string) => {
        setActiveTab(key);
    };

    const handleClearTraining = () => {
        confirmRequest.post({
            message: "确认清除训练信息吗？",
            setPath: "/Jx/ExamSite/Study/clearTraining",
            data: {
                Id: props.Id
            },
            method: "PUT",
            messageApi: messageApi,
            onCallBack: () => {
                formRef.current?.close();
                props.tableRef?.current?.reload();
            }
        });
    };

    const getFooterButtons = () => {
        if (activeTab === "tab1") {
            return [
                <Button
                    color="default" variant="solid"
                    onClick={() => {
                        refundRef.current.open({
                            SaleId: props.Id,
                        });
                    }}
                >
                    退款申请
                </Button>,
                <Button
                    type="primary"
                    key='studyQueue'
                    onClick={() => {
                        confirmRequest.post({
                            message: "确认加入训练排队吗？",
                            setPath: "/Jx/ExamSite/StudyQueen/addQueen",
                            data: {
                                Id: props.Id
                            },
                            method: "PUT",
                            messageApi: messageApi,
                            onCallBack: () => {
                                props.tableRef?.current?.reload();
                            }
                        });
                    }}
                >
                    训练排队
                </Button>,
            ];
        } else if (activeTab === "tab2") {
            return [
                <Button
                    key="clearTraining"
                    type="primary"
                    danger
                    onClick={handleClearTraining}
                >
                    清除训练信息
                </Button>,
                <Button
                    key="printTraining"
                    type="primary"
                    onClick={async () => {
                        const response = await request(`/Jx/ExamSite/SaleInfoPrint/${props.Id}`, {
                            method: "POST",
                        });

                        if (!response) {
                            messageApi.error("获取打印数据失败");
                            return;
                        }
                        const printer = new PrintScan(response.data, false);
                        await printer.print();
                    }}
                    style={{ marginLeft: 10 }}
                >
                    打印训练联
                </Button>,
                <UseSale ref={useSaleRef} Id={props.Id} onSuccess={() => {
                    formRef.current?.close();
                }}>
                    <Button
                        key="clearCoupon"
                        type="primary"
                        style={{ marginLeft: 10 }}
                        onClick={() => {
                            useSaleRef.current?.open();
                        }}
                    >
                        消券
                    </Button>
                </UseSale>
            ];
        } else if (activeTab === "tab3") {
            return [
                <Button
                    key="printCoach"
                    type="primary"
                    onClick={async () => {
                        const response = await request(`/Jx/ExamSite/SaleInfoPrint/${props.Id}`, {
                            method: "POST",
                        });

                        if (!response) {
                            messageApi.error("获取打印数据失败");
                            return;
                        }
                        const printer = new PrintCoupon(response.data, false);
                        await printer.print();
                    }}
                >
                    打印教练联
                </Button>
            ];
        }
        if (activeTab === "tab4") {
            return [
                <Button
                    type="primary"
                    loading={resetPrintLoading}
                    onClick={async () => {
                        setResetPrintLoading(true);
                        try {
                            const response = await request('/Jx/ExamSite/Sale/resetPrint', {
                                method: 'PUT',
                                data: { Id: props.Id }
                            });

                            if (response && response.success) {
                                messageApi.success('重置打印成功');
                            } else {
                                messageApi.error(response?.message || '重置打印失败');
                            }
                        } catch (error) {
                            messageApi.error('重置打印失败');
                            console.error(error);
                        } finally {
                            setResetPrintLoading(false);
                        }
                    }}
                >
                    重置打印
                </Button>];
        }
        return [];
    };

    return (
        <>
            {contextHolder}
            <RefundSale ref={refundRef} Id={props.Id} />
            <MyFormModal
                ref={formRef}
                formItems={[
                    {
                        type: "tab",
                        width: "100%",
                        tabPosition: "top",
                        defaultActiveKey: "tab1",
                        onChange: handleTabChange,
                        tabItems: [
                            {
                                key: "tab1",
                                label: "基本信息",
                                children: [
                                    { name: "ItemName", type: "input", label: "商品名称", width: "100%", required: true, readOnly: true },
                                    { name: "PayMoney", type: "input", label: "付款金额", width: "100%", required: true, readOnly: true },

                                    { name: "xm", type: "input", label: "学员姓名", width: "100%", required: true, readOnly: true },
                                    { name: "sfzmhm", type: "input", label: "证件号码", width: "100%", required: true, readOnly: true },
                                    { name: "yddh", type: "input", label: "手机号码", width: "100%", required: true, readOnly: true },
                                    { name: "ExamDate", type: "input", label: "考试日期", width: "100%", required: true, readOnly: true },
                                    {
                                        name: "Id",
                                        type: "input",
                                        label: "电券编号",
                                        width: "100%",
                                        required: true,
                                        readOnly: true,
                                        compact: <Button onClick={() => {
                                            const value = formRef.current?.getValue("Id");
                                            if (value) {
                                                navigator.clipboard.writeText(value);
                                                messageApi.success("电券编号已复制");
                                            }
                                        }}>复制</Button>
                                    },
                                    {
                                        name: "OrderId",
                                        type: "input",
                                        label: "订单编号",
                                        width: "100%",
                                        required: true,
                                        readOnly: true,
                                        compact: <Button onClick={() => {
                                            const value = formRef.current?.getValue("OrderId");
                                            if (value) {
                                                navigator.clipboard.writeText(value);
                                                messageApi.success("订单编号已复制");
                                            }
                                        }}>复制</Button>
                                    },
                                    {
                                        name: "TenantId",
                                        type: "input",
                                        label: "公司编号",
                                        width: "100%",
                                        required: true,
                                        readOnly: true,
                                        compact: <Button onClick={() => {
                                            const value = formRef.current?.getValue("TenantId");
                                            if (value) {
                                                navigator.clipboard.writeText(value);
                                                messageApi.success("公司编号已复制");
                                            }
                                        }}>复制</Button>
                                    },
                                ],
                            },
                            {
                                key: "tab2",
                                label: "训练条码",
                                children: [
                                    {
                                        type: "qrcode",
                                        name: "",
                                        label: "",
                                        qrValue: "https://51jx.cc/Self/ExamSite/Sale?Id=" + props.Id.toString().toUpperCase() + "&TenantId=" + props.TenantId.toString(),
                                        qrSize: 200,
                                        errorLevel: "L",
                                        color: "#000000",
                                        bgColor: "#ffffff",
                                        iconSize: 40,
                                        bordered: false,
                                        style: { margin: "0" },
                                    },
                                ],
                            },
                            {
                                key: "tab3",
                                label: "返利条码",
                                children: [
                                    {
                                        type: "qrcode",
                                        name: "",
                                        label: "",
                                        qrValue: "https://51jx.cc/Self/ExamSite/Coupon?Id=" + props.Id.toString().toUpperCase() + "&TenantId=" + props.TenantId.toString(),
                                        qrSize: 200,
                                        errorLevel: "L",
                                        color: "#000000",
                                        bgColor: "#ffffff",
                                        iconSize: 40,
                                        bordered: false,
                                        style: { margin: "0" },
                                    },
                                ],
                            },
                            {
                                key: "tab4",
                                label: "打印记录",
                                children: [
                                    {
                                    },
                                ],
                            },
                        ],
                    },
                ]}
                modifyTitle="电券信息"
                insertTitle="电券信息"
                initData={{ Id: undefined, SortCode: 9999 }}
                getPath={`/Jx/ExamSite/SaleInfo/${props.Id}`}
                onLoad={() => { }}
                width={500}
                onCallBack={() => { }}
                setPath=""
                footer={getFooterButtons()}
                hideOkCancel={true}
            />
            <a
                onClick={() => {
                    formRef.current.open(props.Id);
                }}
            >
                {props.children}
            </a>
        </>
    );
});

export default SaleInfo;
