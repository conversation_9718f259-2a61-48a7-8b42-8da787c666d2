import MyFormModal from "@/components/CustomComponent/MyFormModal/index";
import MyTable, { MyTableColumnProps } from "@/components/CustomComponent/MyTable/index";
import JxClassConfig from "./JxClassConfig";
import ESignConfigModal from "./ESignConfigModal";
import TemplateSelectModal from "./TemplateSelectModal";
import TemplateComponentsModal from "./TemplateComponentsModal";
import { PlusCircleOutlined } from "@ant-design/icons";
import { PageContainer } from "@ant-design/pro-components";
import { Button, message, Space, Tag } from "antd";
import React, { useRef, useState } from "react";
import confirmRequest from "@/utils/confirmRequest";
import request from "@/utils/request";

interface TemplateItem {
    DocTemplateName: string;
    DocTemplateId: string;
}

interface TemplateComponent {
    ComponentId: string;
    ComponentKey: string | null;
    ComponentName: string;
}

const JxClassList: React.FC = () => {
    const [messageApi, contextHolder] = message.useMessage();
    const tableRef = useRef<any>();
    const formRef = useRef<any>();
    const [loading, setLoading] = useState(false);
    const [templateLoading, setTemplateLoading] = useState(false);
    const [templateModalVisible, setTemplateModalVisible] = useState(false);
    const [templateList, setTemplateList] = useState<TemplateItem[]>([]);
    const [templateComponents, setTemplateComponents] = useState<TemplateComponent[]>([]);
    const [componentsModalVisible, setComponentsModalVisible] = useState(false);
    const [componentValues, setComponentValues] = useState<Record<string, string>>({});
    const [configModalVisible, setConfigModalVisible] = useState(false);

    const getTemplateInfo = () => {
        const templateId = formRef.current?.getValue('ESignTemplateId');
        if (!templateId) {
            messageApi.warning('请输入模板编号');
            return;
        }
        setLoading(true);
        request<{
            success: boolean;
            message?: string;
            data?: TemplateComponent[];
        }>('/JiaXiao/ESignTemplate/getTemplateComponents', {
            method: 'POST',
            data: { ESignTemplateId: templateId }
        }).then((res) => {
            if (res.success && res.data) {
                setTemplateComponents(res.data);
                setComponentsModalVisible(true);
                messageApi.success('获取模板信息成功');
            } else {
                messageApi.error(res.message || '获取模板信息失败');
            }
        }).catch((error: Error) => {
            messageApi.error('获取模板信息失败');
            console.error(error);
        }).finally(() => {
            setLoading(false);
        });
    };

    const getTemplateList = () => {
        setTemplateModalVisible(true);
        setTemplateLoading(true);
        request<{
            success: boolean;
            message?: string;
            data?: TemplateItem[];
        }>('/JiaXiao/ESignTemplate/getTemplateList', {
            method: 'POST',
            data: {}
        }).then((res) => {
            if (res.success && res.data) {
                setTemplateList(res.data);
            } else {
                setTemplateList([]);
                messageApi.error(res.message || '获取模板列表失败');
            }
        }).catch((error: Error) => {
            setTemplateList([]);
            messageApi.error('获取模板列表失败');
            console.error(error);
        }).finally(() => {
            setTemplateLoading(false);
        });
    };

    const handleSelectTemplate = (template: TemplateItem) => {
        formRef.current?.setValues({
            ESignTemplateId: template.DocTemplateId
        });
        setTemplateModalVisible(false);
        messageApi.success(`已选择模板: ${template.DocTemplateName}`);
    };

    const editInfo = (record: any) => (
        <a
            key="edit"
            onClick={() => {
                formRef.current.get(record.Id);
            }}
            title="编辑"
        >
            {record.Name}
        </a>
    );

    const status = (record: any) => (
        <Space>
            {(() => {
                const renderTag = (color: any, text: any, onClick: any) => (
                    <a onClick={onClick}>
                        <Tag color={color} key={"name"} style={{ padding: "0px 14px" }}>
                            {text}
                        </Tag>
                    </a>
                );

                switch (record.Status) {
                    case "Run":
                        return renderTag("green", "正常", () => closeJxClass(record.Id, record.Name));
                    case "NoStart":
                        return renderTag("blue", "未开始", () => openJxClass(record.Id, record.Name));
                    case "End":
                        return renderTag("red", "结束", () => openJxClass(record.Id, record.Name));
                    case "Other":
                        return (
                            <Tag color="gray" key="name" style={{ padding: "2px 14px" }}>
                                异常
                            </Tag>
                        );
                    default:
                        return null;
                }
            })()}
        </Space>
    );

    const closeJxClass = (Id: string, Name: string) => {
        confirmRequest.post({
            message: `是否确认关闭 ${Name} ?`,
            setPath: "/JiaXiao/JxClass/closeJxClass",
            data: { Id: Id },
            method: "PUT",
            messageApi,
            onCallBack: () => {
                tableRef.current.reload();
            },
        });
    };
    const openJxClass = (Id: string, Name: string) => {
        confirmRequest.post({
            message: `是否确认打开 ${Name} ?`,
            setPath: "/JiaXiao/JxClass/openJxClass",
            data: { Id: Id },
            method: "PUT",
            messageApi,
            onCallBack: () => {
                tableRef.current.reload();
            },
        });
    };

    const columns: MyTableColumnProps[] = [
        { dataIndex: "RowIndex", title: "序号", fixed: "left", align: "center" },
        { dataIndex: "Name", title: "班别名称", align: "center", render: (_: any, record: any) => editInfo(record) },
        { title: "配置", align: "center", render: (_: any, record: any) => <JxClassConfig JxClassId={record.Id} /> },
        {
            dataIndex: "Id",
            title: "删除",
            fixed: "left",
            align: "center",
            deleteColumn: true,
            deletePath: "/JiaXiao/JxClassInfo/{Id}",
            deleteMessage: "确认删除该班型?",
        },
        { dataIndex: "SortCode", title: "显示排序", align: "center" },
        { dataIndex: "Status", title: "状态", align: "center", render: (_: any, record: any) => status(record) },
        { dataIndex: "CreateUserName", title: "创建人", align: "center" },
        { dataIndex: "CreateTime", title: "创建时间", align: "center" },
    ];

    const tableButtons: any[] = [
        <Button key="jx-area-add" icon={<PlusCircleOutlined />} style={{ marginLeft: "10px" }} type="primary" onClick={() => formRef.current.get(undefined)}>
            添加
        </Button>,
    ];

    return (
        <PageContainer header={{ breadcrumb: {}, title: "" }}>
            {contextHolder}
            <MyTable
                key="jx-jxclass-list-table"
                ref={tableRef}
                formCols={[]}
                columns={columns}
                tableButtons={tableButtons}
                getPath="/JiaXiao/JxClass/getJxClassList"
                rowKey={"Id"}
            ></MyTable>

            <MyFormModal
                ref={formRef}
                formItems={[
                    { name: "Id", type: "hidden", label: "", placeholder: "", required: false },
                    {
                        type: "group",
                        children: [
                            { name: "Name", type: "input", label: "班级名称", placeholder: "请输入班级名称", width: 400, required: true },
                            { name: "SortCode", type: "number", label: "显示排序", placeholder: "请输入显示排序的数字", width: 100, required: true },
                        ],
                    },
                    { name: "AllowTime", type: "daterange", label: "生效时间", placeholder: "请选择正确的生效时间", required: true, width: 583 },
                    {
                        type: "group",
                        children: [
                            { name: "MinAge", type: "number", label: "最小年龄", placeholder: "请输入最小年龄", width: 248, required: true },
                            { name: "MaxAge", type: "number", label: "最大年龄", placeholder: "请输入最大年龄", width: 248, required: true },
                        ],
                    },
                    // { name: "ESignOrgId", type: "input", label: "机构编号", placeholder: "请输入e签宝机构编号", required: false },

                    {
                        name: "ESignTemplateId", type: "input", label: "合同设置", width: '100%', placeholder: "请输入e签宝合同编号", required: false,
                        compact: <>
                            <Button type="primary" onClick={() => setConfigModalVisible(true)} loading={false}>配置</Button>
                            <Button type="primary" onClick={getTemplateList} loading={templateLoading}>选择模板</Button>
                            <Button type="primary" onClick={getTemplateInfo} loading={loading}>设置模板</Button>
                        </>
                    },

                    { name: "Remark", type: "textarea", label: "备注信息", placeholder: "请输入备注信息", required: false, height: 80 },
                    {
                        name: "PrintRemark",
                        type: "textarea",
                        label: "打印备注",
                        placeholder: "请输入培训费打印备注,在培学员的培训费打印摘要会·打印出来",
                        required: false,
                        height: 80,
                    },
                    {
                        name: "PrintWaitStudyRemark",
                        type: "textarea",
                        label: "待培打印",
                        placeholder: "待培学员打印备注,待培学员的培训费打印摘要会打印出来",
                        required: false,
                        height: 80,
                    },
                ]}
                modifyTitle={"报名班型编辑"}
                insertTitle={"添加报名班型"}
                initData={{ Id: undefined, SortCode: 9999, MinAge: 0, MaxAge: 100, AllowTime: [new Date(), new Date("2099-01-01")] }}
                getPath={"/JiaXiao/JxClassInfo/{id}"}
                setPath={"/JiaXiao/JxClassInfo/{id}"}
                width={700}
                onCallBack={() => {
                    tableRef.current.reload();
                }}
            ></MyFormModal>

            <TemplateSelectModal
                open={templateModalVisible}
                onOpenChange={setTemplateModalVisible}
                templateList={templateList}
                loading={templateLoading}
                onSelectTemplate={handleSelectTemplate}
            />

            <TemplateComponentsModal
                open={componentsModalVisible}
                onOpenChange={setComponentsModalVisible}
                templateComponents={templateComponents}
                componentValues={componentValues}
                setComponentValues={setComponentValues}
                messageApi={messageApi}
            />

            <ESignConfigModal
                open={configModalVisible}
                onOpenChange={setConfigModalVisible}
                onSuccess={() => {
                    messageApi.success('电子签章配置已更新');
                }}
            />
        </PageContainer>
    );
};
export default JxClassList;
