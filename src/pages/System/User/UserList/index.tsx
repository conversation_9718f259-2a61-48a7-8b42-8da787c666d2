import MyTable, { MyTableColumnProps } from "@/components/CustomComponent/MyTable/index";
import { PageContainer } from "@ant-design/pro-components";
import { Button, message, Modal } from "antd";
import React, { useEffect, useRef, useState } from "react";
import JxStudent from "@/components/JiaXiao/Student";
import ChangePWD from "./ChangePWD";
import BindUserQCode from "./BindUserQCode";
import UserEnabled from "./UserEnabled";
import UserInfo from "./UserInfo";
import { request } from "@/utils/request";
import { getRoleSelectList } from "@/services/ant-design-pro/api";
import { getJxDeptSelectList, getJxFieldSelectList } from "@/services/select/jiaXiao";
import { getCategoryDetailTreeList } from "@/services/select/sys";
import { PlusCircleOutlined, QrcodeOutlined } from "@ant-design/icons";
import { FormItem } from "@/components/CustomComponent/DynamicFormBuilder/index";
import ScanRegister from './ScanRegister';

const UserList: React.FC = () => {
    const [messageApi, contextHolder] = message.useMessage();

    const tableRef = useRef<any>();
    const studentInfoRef: any = useRef(null);
    const addUserFormRef: any = useRef(null);

    const showInfo = (record: any) => <UserInfo userId={record.Id} realName={record.RealName} messageApi={messageApi} />;

    const changePWD = (record: any) => <ChangePWD userId={record.Id} />;
    const bindUserQCode = (record: any) => <BindUserQCode userId={record.Id} realName={record.RealName} />;
    const userEnabled = (record: any) => (
        <UserEnabled
            userId={record.Id}
            realName={record.RealName}
            isEnabled={record.IsEnabled}
            onCallBack={() => {
                tableRef.current.reload();
            }}
            messageApi={messageApi}
        />
    );

    const [columns, setColumns] = React.useState<any[]>([]);
    const [myColumns, setMyColumns] = React.useState<any[]>([]);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [scanRegisterOpen, setScanRegisterOpen] = useState(false);

    // 添加选中行的状态管理
    const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
    const [selectedRows, setSelectedRows] = useState<any[]>([]);

    // 修改批量操作的定义
    const batchOperations = [
        {
            key: 'enable',
            label: '批量启用',
            onClick: (selectedKeys: React.Key[], selectedRows: any[]) => {
                let confirmModal: any;
                let progress = 0;
                const total = selectedKeys.length;

                confirmModal = Modal.confirm({
                    title: '确认操作',
                    content: `确认要启用选中的 ${selectedKeys.length} 个用户吗？`,
                    okButtonProps: { loading: false },
                    onOk: async () => {
                        // 点击确认后更新 Modal 内容，而不是创建新的 Modal
                        confirmModal.update({
                            title: '正在批量启用用户',
                            content: `处理进度：${progress}/${total}`,
                            okButtonProps: { style: { display: 'none' } },
                            cancelButtonProps: { style: { display: 'none' } }
                        });

                        // 递归处理每个用户
                        const processUser = async (index: number) => {
                            if (index >= selectedKeys.length) {
                                confirmModal.destroy();
                                messageApi.success('批量启用完成！');
                                setSelectedRowKeys([]);
                                setSelectedRows([]);
                                tableRef.current.reload();
                                return;
                            }

                            try {
                                await request('/SystemManage/User/openUser', {
                                    method: 'PUT',
                                    data: { id: selectedKeys[index] }
                                });

                                progress++;
                                confirmModal.update({
                                    content: `处理进度：${progress}/${total}`,
                                });

                                await processUser(index + 1);
                            } catch (error) {
                                confirmModal.destroy();
                                messageApi.error('操作过程中出现错误');
                            }
                        };

                        await processUser(0);
                    }
                });
            }
        },
        {
            key: 'disable',
            label: '批量禁用',
            onClick: (selectedKeys: React.Key[], selectedRows: any[]) => {
                let confirmModal: any;
                let progress = 0;
                const total = selectedKeys.length;

                confirmModal = Modal.confirm({
                    title: '确认操作',
                    content: `确认要禁用选中的 ${selectedKeys.length} 个用户吗？`,
                    okButtonProps: { loading: false },
                    onOk: async () => {
                        // 点击确认后更新 Modal 内容
                        confirmModal.update({
                            title: '正在批量禁用用户',
                            content: `处理进度：${progress}/${total}`,
                            okButtonProps: { style: { display: 'none' } },
                            cancelButtonProps: { style: { display: 'none' } }
                        });

                        // 递归处理每个用户
                        const processUser = async (index: number) => {
                            if (index >= selectedKeys.length) {
                                confirmModal.destroy();
                                messageApi.success('批量禁用完成！');
                                setSelectedRowKeys([]);
                                setSelectedRows([]);
                                tableRef.current.reload();
                                return;
                            }

                            try {
                                await request('/SystemManage/User/closeUser', {
                                    method: 'PUT',
                                    data: { id: selectedKeys[index] }
                                });

                                progress++;
                                confirmModal.update({
                                    content: `处理进度：${progress}/${total}`,
                                });

                                await processUser(index + 1);
                            } catch (error) {
                                confirmModal.destroy();
                                messageApi.error('操作过程中出现错误');
                            }
                        };

                        await processUser(0);
                    }
                });
            }
        }
    ];

    // 添加行选择配置
    const rowSelection = {
        selectedRowKeys,
        setSelectedRowKeys,
        onChange: (selectedKeys: React.Key[], rows: any[]) => {
            setSelectedRowKeys(selectedKeys);
            setSelectedRows(rows);
        }
    };

    useEffect(() => {
        const fetchData = async () => {
            request<API.Result<any>>("/SystemManage/Category/getCategoryList", {
                method: "POST",
            }).then((json) => {
                let _columns: any[] = [];

                json.data.map((item: any) => {
                    _columns.push({
                        dataIndex: `Column-${item.Id}`,
                        title: item.Name,
                        align: "center",
                        search: false,
                    });
                });

                console.log('_columns:' + _columns);

                setMyColumns(_columns);

                setColumns([
                    { title: "序号", dataIndex: "RowIndex", align: "center" },
                    { title: "编号", dataIndex: "SysId", align: "center" },
                    { title: "账号", dataIndex: "Account", align: "center" },
                    { title: "名字", dataIndex: "RealName", render: (dom: any, record: any) => showInfo(record), align: "center" },
                    { title: "电话", dataIndex: "Phone", align: "center" },
                    { title: "车牌", dataIndex: "CarNumbers", align: "center" },
                    { title: "密码", render: (dom: any, record: any) => changePWD(record), align: "center" },
                    { title: "微信", render: (dom: any, record: any) => bindUserQCode(record), align: "center" },
                    { title: "状态", render: (dom: any, record: any) => userEnabled(record), align: "center" },
                    { title: "公司名称", dataIndex: "CompanyName", align: "center" },
                    { title: "培训场地", dataIndex: "JxDeptName", align: "center" },
                    { title: "训练场地", dataIndex: "JxFieldName", align: "center" },
                    ..._columns,
                    { title: "创建人", dataIndex: "CreateUserName", align: "center" },
                    { title: "创建时间", dataIndex: "CreateTime", align: "center" },
                ]);
            });
        };
        fetchData();
    }, []);

    const formCols: FormItem[] = [
        {
            name: "SearchKey",
            type: "input",
            label: "关键字词",
            placeholder: "请输入名字、账号、电话等",
            allowClear: true,
        },
        {
            name: "IsEnableds",
            type: "select",
            label: "用户状态",
            placeholder: "请选择用户状态",
            options: [
                { label: "有效", value: "1" },
                { label: "无效", value: "0" },
            ],
            value: ["1"],
            multiple: true,
            allowClear: true,
        },
        {
            name: "IsAdmins",
            type: "select",
            label: "最高权限",
            placeholder: "选择是否有最高权限的",
            options: [
                { label: "是", value: "1" },
                { label: "否", value: "0" },
            ],
            allowClear: true,
            onChange: () => { },
        },
        {
            name: "RoleId",
            type: "select",
            label: "用户角色",
            placeholder: "选择用户角色来查询",
            request: getRoleSelectList,
            allowClear: true,
            onChange: () => { },
        },
        {
            name: "JxDeptIds",
            type: "select",
            label: "所属门店",
            placeholder: "选择用户所属门店",
            request: getJxDeptSelectList,
            multiple: true,
            allowClear: true,
            onChange: () => { },
        },
        {
            name: "JxFieldIds",
            type: "select",
            label: "所属场地",
            placeholder: "选择用户所属场地",
            request: getJxFieldSelectList,
            allowClear: true,
            onChange: () => { },
        },
        {
            name: "CategoryId",
            type: "selecttree",
            label: "用户分类",
            placeholder: "选择用户分类",
            request: getCategoryDetailTreeList,
            maxTagCount: 1,
            allowClear: true,
            onChange: (e) => {
                tableRef.current.setFormData("CategoryId", e);
            },
        },
        {
            name: "CategoryParentId",
            type: "selecttree",
            label: "分类上级",
            placeholder: "选择用户分类上级",
            request: getCategoryDetailTreeList,
            maxTagCount: 1,
            allowClear: true,
            onChange: (e) => {
                tableRef.current.setFormData("CategoryParentId", e);
            },
        },
    ];

    const handleScanRegister = () => {
        setIsModalOpen(false);
        setScanRegisterOpen(true);
    };

    const handleScanRegisterCancel = () => {
        setScanRegisterOpen(false);
    };

    const showModal = () => {
        setIsModalOpen(true);
    };

    const handleCancel = () => {
        setIsModalOpen(false);
    };

    const tableButtons: any[] = [
        <Button
            key="add-button"
            icon={<PlusCircleOutlined />}
            type="primary"
            onClick={showModal}
            ghost
        >
            添加
        </Button>,
        <Modal
            key="add-modal"
            title="选择添加方式"
            open={isModalOpen}
            onCancel={handleCancel}
            footer={null}
            centered
        >
            <div style={{ display: 'flex', justifyContent: 'space-around', padding: '20px' }}>
                <Button
                    type="primary"
                    icon={<PlusCircleOutlined />}
                    onClick={() => {
                        addUserFormRef.current.open();
                        setIsModalOpen(false);
                    }}
                    size="large"
                    style={{ height: '45px', width: '140px', fontSize: '16px' }}
                >
                    直接添加
                </Button>
                <Button
                    type="primary"
                    icon={<QrcodeOutlined />}
                    onClick={handleScanRegister}
                    size="large"
                    style={{ height: '45px', width: '140px', fontSize: '16px' }}
                >
                    扫码注册
                </Button>
            </div>
        </Modal>
    ];

    return (
        <PageContainer header={{ breadcrumb: {}, title: "" }}>
            {contextHolder}
            {
                columns.length > 0 &&
                <MyTable
                    key="my-list-table"
                    ref={tableRef}
                    formCols={formCols}
                    columns={columns}
                    tableButtons={tableButtons}
                    getPath="/SystemManage/User/getUserList"
                    downloadPath="/SystemManage/User/exportUserList"
                    downloadTableName="UserList"
                    rowKey={"Id"}
                    rowSelection={rowSelection}
                    batchOperations={batchOperations}
                    transformedData={(data: any[]) => {
                        data.map((item: any) => {
                            if (item.CategoryDatas && Array.isArray(item.CategoryDatas)) {
                                // Group categories by CategoryId
                                const categoryGroups: Record<string, string[]> = {};

                                item.CategoryDatas.forEach((category: any) => {
                                    if (!categoryGroups[category.CategoryId]) {
                                        categoryGroups[category.CategoryId] = [];
                                    }
                                    categoryGroups[category.CategoryId].push(category.CategoryName);
                                });

                                // Set the joined category names for each CategoryId
                                Object.keys(categoryGroups).forEach((categoryId) => {
                                    item[`Column-${categoryId}`] = categoryGroups[categoryId].join(', ');
                                });
                            }
                        });

                        return data;
                    }}
                ></MyTable>
            }

            <JxStudent ref={studentInfoRef} StudentListRef={undefined} updateAddLoading={undefined} />
            <UserInfo userId={undefined} realName={undefined} ref={addUserFormRef} messageApi={messageApi} />

            <ScanRegister
                open={scanRegisterOpen}
                onCancel={handleScanRegisterCancel}
            />
        </PageContainer>
    );
};

export default UserList;
