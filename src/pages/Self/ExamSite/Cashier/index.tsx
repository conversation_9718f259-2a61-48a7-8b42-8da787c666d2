import React, { useState, useRef, useEffect } from "react";
import { ConfigProvider, message, Button, notification, Result, Modal } from "antd";
import { ClearOutlined, RedoOutlined, CarOutlined } from "@ant-design/icons";
import { useBarCodeScanner } from "./hooks/useBarCodeScanner";
import { usePreventZoom } from "./hooks/usePreventZoom";
import * as api from "./api/cashierApi";
import { InitializationScreen } from "./components/InitializationScreen";
import { ErrorScreen } from "./components/ErrorScreen";
import { PaymentQRCode } from "./components/PaymentQRCode";
import { FieldInfo } from "./types";
import "./index.less";
import { getMac, printExamSiteSale } from "@/utils/pandaThird";
import ExitModal from "./components/ExitModal";
import CarSelector from "./components/CarSelector";
import { request } from "@/utils/request";
import { PrintScan } from "@/services/print/scan";
import { PrintMiniScan } from "@/services/print/miniScan";
import { readScanResult } from "@/utils/pandaThird";
import { PrintCoupon } from "@/services/print/coupon";
import { TestPrinter } from "@/services/print/TestPrinter";

const Cashier: React.FC = () => {
    const [messageApi, contextHolder] = message.useMessage();
    const [fieldId, setFieldId] = useState("start");
    const [fieldInfo, setFieldInfo] = useState<FieldInfo>();
    const [deviceId, setDeviceId] = useState("");
    const [stepIndex, setStepIndex] = useState(0);
    const stepIndexRef = useRef(stepIndex);
    const [paying, setPaying] = useState(false);
    const payingRef = useRef(paying);
    const [payloading, setPayloading] = useState("请将付款码放盖到扫码盒上");
    const [fieldName, setFieldName] = useState("");
    const [barCodeImage, setBarCodeImage] = useState("");
    const [showCarSelector, setShowCarSelector] = useState(false);
    const [selectedCarId, setSelectedCarId] = useState<string>("");
    const [countdown, setCountdown] = useState<number>(0);
    const countdownRef = useRef<NodeJS.Timeout>();
    const [currentUserId, setCurrentUserId] = useState("");
    const [initStatus, setInitStatus] = useState<"initializing" | "error" | "success">("initializing");
    const [initError, setInitError] = useState<string>("");
    const [isProcessing, setIsProcessing] = useState(false);
    const [isScanning, setIsScanning] = useState(false);
    const scanTimeoutRef = useRef<NodeJS.Timeout>();

    usePreventZoom();

    useEffect(() => {
        stepIndexRef.current = stepIndex;
    }, [stepIndex]);

    useEffect(() => {
        payingRef.current = paying;
    }, [paying]);

    useEffect(() => {
        return () => {
            if (countdownRef.current) {
                clearInterval(countdownRef.current);
            }
            if (scanTimeoutRef.current) {
                clearTimeout(scanTimeoutRef.current);
            }
        };
    }, []);

    useEffect(() => {
        document.title = "收银台";
        initializeDevice();

        // 打印测试票据
        printTestReceipt();
    }, []);

    // 打印测试票据
    const printTestReceipt = async () => {
        try {
            const testPrinter = new TestPrinter();
            await testPrinter.print();
        } catch (error) {
            console.error("打印测试票据失败:", error);
            notification.error({
                message: "打印测试失败",
                description: "打印测试票据失败，请检查打印机连接",
                duration: 3,
                placement: "top",
            });
        }
    };

    // 启动扫码结果轮询
    useEffect(() => {
        if (initStatus === "success") {
            startScanPolling();
        }
        return () => {
            if (scanTimeoutRef.current) {
                clearTimeout(scanTimeoutRef.current);
            }
        };
    }, [initStatus]);

    const startScanPolling = () => {
        if (isScanning) return;

        setIsScanning(true);
        pollScanResult();
    };

    const pollScanResult = async () => {
        try {
            const result = await readScanResult();
            if (result && typeof result === 'string' && result.length > 0) {
                console.log("扫码结果:", result);
                handleBarCodeScanned(result);
            }
        } catch (error) {
            console.error("读取扫码结果失败:", error);
        } finally {
            // 50ms后再次执行
            scanTimeoutRef.current = setTimeout(() => {
                pollScanResult();
            }, 100);
        }
    };

    const initializeDevice = async () => {
        try {
            // 第一步：获取MAC地址
            const mac = await getMac();
            if (mac == undefined) {
                setInitStatus("error");
                setInitError("获取设备信息失败");
                return;
            }

            // 第二步：根据MAC地址获取设备信息
            const deviceResponse = await api.getJxDeviceInfo(mac);
            if (!deviceResponse?.success) {
                setInitStatus("error");
                setInitError(deviceResponse?.message || "获取设备配置失败");
                return;
            }

            // 设置设备ID并继续初始化流程
            const deviceId = deviceResponse.data.Id;
            if (deviceId) {
                setDeviceId(deviceId);
                await handleGetFieldInfo(deviceId);
                setInitStatus("success");
            } else {
                setInitStatus("error");
                setInitError("设备未配置");
            }
        } catch (error) {
            setInitStatus("error");
            setInitError("系统初始化失败，请检查网络连接");
        }
    };

    const handleBarCodeScanned = async (barcode: unknown) => {
        console.error("barcode:", barcode);
        // 确保 barcode 是字符串类型
        if (!barcode || typeof barcode !== "string") {
            console.error("Invalid barcode:", barcode);
            return;
        }

        // 如果有请求正在处理中，不执行新的操作
        if (isProcessing) {
            notification.warning({
                message: "请稍候",
                description: "上一个操作正在处理中，请稍后再试",
                duration: 3,
                placement: "top",
            });
            return;
        }

        if (barcode.startsWith("UserId#")) {
            // 检查状态
            if (showCarSelector) {
                // 如果车辆选择窗口打开，不执行任何操作
                return;
            } else if (countdown > 0) {
                // 如果倒计时窗口打开，不执行任何操作
                return;
            }
            // 如果都没有打开，才处理工牌
            setIsProcessing(true);
            try {
                const userId = barcode.substring(7); // 去掉 'UserId#' 前缀
                setCurrentUserId(userId); // 存储 UserId
                setShowCarSelector(true);
            } finally {
                setIsProcessing(false);
            }
        } else if (barcode.startsWith("https://51jx.cc/Self/ExamSite/Sale") || barcode.startsWith("https://mand.122erp.com/#/SaleFullInfo?Id=")) {
            try {
                const url = new URL(barcode);
                const saleId = url.searchParams.get("Id");
                if (saleId) {
                    // 检查状态
                    if (showCarSelector) {
                        // 如果车辆选择窗口打开，不执行任何操作
                        return;
                    } else if (countdown > 0) {
                        // 如果倒计时窗口打开，先暂停倒计时
                        if (countdownRef.current) {
                            clearInterval(countdownRef.current);
                        }

                        setIsProcessing(true);
                        try {
                            // 调用消券API
                            const response = await api.useCoupon({
                                SaleId: saleId,
                                UserId: currentUserId,
                                CarId: selectedCarId,
                            });

                            if (response && response.success) {
                                notification.success({
                                    message: "消券成功",
                                    description: response.message || "电子券使用成功",
                                    duration: 3,
                                    placement: "top",
                                });
                            } else {
                                notification.error({
                                    message: "消券失败",
                                    description: response?.message || "电子券使用失败",
                                    duration: 3,
                                    placement: "top",
                                });
                            }
                            // 无论成功失败都重新开始倒计时
                            startCountdown(20);
                        } catch (error) {
                            console.error("电子券使用失败:", error);
                            notification.error({
                                message: "消券失败",
                                description: "电子券使用请求失败",
                                duration: 3,
                                placement: "top",
                            });
                            // 发生错误时也重新开始倒计时
                            startCountdown(20);
                        } finally {
                            setIsProcessing(false);
                        }
                        return;
                    }
                    // 如果都没有打开，执行打印
                    setIsProcessing(true);
                    try {
                        await handlePrintReceipt(saleId);
                    } finally {
                        setIsProcessing(false);
                    }
                }
            } catch (error) {
                console.error("Invalid URL:", error);
                notification.error({
                    message: "错误",
                    description: "二维码格式错误",
                    duration: 3,
                    placement: "top",
                });
                setIsProcessing(false);
            }
        } else if (isPaymentCode(barcode)) {
            setIsProcessing(true);
            try {
                handlePaymentCode();
            } finally {
                setIsProcessing(false);
            }
        }
    };

    const handlePrintReceipt = async (saleId: string) => {
        setIsPrinting(true); // 显示打印弹窗

        try {
            const mac = await getMac();
            if (!mac) {
                notification.error({
                    message: "错误",
                    description: "获取打印机信息失败",
                    duration: 3,
                    placement: "top",
                });
                return;
            }

            messageApi.loading({ content: "正在获取打印数据", key: "loading", duration: 0 });
            // 从API获取打印数据
            const response = await request(`/Jx/ExamSite/SaleInfoPrint/${saleId}`, {
                method: "POST",
            });

            if (!response) {
                messageApi.destroy("loading");
                messageApi.open({
                    type: 'error',
                    content: "获取打印数据失败",
                    duration: 3,
                });
                return;
            }
            if (!response.success) {
                messageApi.destroy("loading");
                messageApi.open({
                    type: 'error',
                    content: response.message,
                    duration: 3,
                });
                return;
            }

            messageApi.loading({ content: "正在打印", key: "loading", duration: 0 });

            if (response.data.NeedPrint) {
                // 创建复联打印实例
                const printer = new PrintScan(response.data, false);
                await printer.print();
            }

            for (var i = 0; i < response.data.PrintMiniTicket; i++) {

                // 创建小型复联打印实例
                const miniScanPrinter = new PrintMiniScan(response.data, false);
                await miniScanPrinter.print();
            }

            ///有返利的  打印 教练返利联
            if (response.data.NeedCoupon > 0 || response.data.NeedCouponPoint > 0) {
                // 创建教练返利打印实例
                const couponPrinter = new PrintCoupon(response.data, false);
                await couponPrinter.print();
            }

            messageApi.destroy("loading");

            // const result = await printExamSiteSale(saleId); // 使用存储的 UserId
            // if (result) {
            //     notification.success({
            //         message: "打印成功",
            //         description: "打印成功",
            //         duration: 3,
            //         placement: "top",
            //     });
            // }
        } catch (error) {
            console.error("打印失败:", error);
            notification.error({
                message: "打印失败",
                description: "打印失败",
                duration: 3,
                placement: "top",
            });
        } finally {
            // 无论成功失败,延迟一段时间后关闭弹窗,让用户能看到结果
            setTimeout(() => {
                setIsPrinting(false);
            }, 1500);
        }
    };

    // const isScanning = useBarCodeScanner(handleBarCodeScanned);

    const isPaymentCode = (code: string): boolean => {
        const wechatPayRegex = /^10\d{16}$/;
        const alipayRegex = /^(25|28)\d{14,22}$/;
        return wechatPayRegex.test(code) || alipayRegex.test(code);
    };

    const handlePaymentCode = () => {
        if (stepIndexRef.current === 2) {
            setPaying(true);
            setPayloading("请完成手机上支付操作");
        } else if (payingRef.current) {
            notification.error({
                message: "错误",
                description: "请勿重复扫码",
                duration: 3,
                placement: "top",
            });
        } else {
            notification.error({
                message: "错误",
                description: "请勿直接扫支付码",
                duration: 3,
                placement: "top",
            });
        }
    };

    const handleGetFieldInfo = async (id: string) => {
        messageApi.loading({ content: "系统正在登录", key: "loading", duration: 0 });
        const response = await api.getFieldInfo(id);
        messageApi.destroy("loading");

        if (response?.success) {
            setFieldName(response.data.NickName);
            setFieldId(response.data.Id);
            setFieldInfo(response.data);
            handleGetFieldSelfLogin();
        } else {
            setFieldId("");
            notification.error({
                message: "登录失败",
                description: response?.message,
                duration: 3,
                placement: "top",
            });
        }
    };

    const handleGetFieldSelfLogin = async () => {
        messageApi.loading({ content: "正在生成二维码", key: "loading", duration: 0 });
        const response = await api.getFieldSelfLogin();
        messageApi.destroy("loading");

        if (response?.success) {
            setBarCodeImage(response.data);
        } else {
            notification.error({
                message: "读取二维码失败",
                description: response?.message,
                duration: 3,
                placement: "top",
            });
        }
    };

    const startCountdown = (seconds: number) => {
        if (countdownRef.current) {
            clearInterval(countdownRef.current);
        }
        setCountdown(seconds);
        countdownRef.current = setInterval(() => {
            setCountdown((prev) => {
                if (prev <= 1) {
                    if (countdownRef.current) {
                        clearInterval(countdownRef.current);
                    }
                    return 0;
                }
                return prev - 1;
            });
        }, 1000);
    };

    const handleExitCountdown = () => {
        if (countdownRef.current) {
            clearInterval(countdownRef.current);
        }
        setCountdown(0);
        setSelectedCarId("");
        setCurrentUserId("");
        setIsProcessing(false);
    };

    const renderInitializationScreen = () => {
        switch (initStatus) {
            case "initializing":
                return (
                    <div className="center-container">
                        <div className="center-content">
                            <Result title="系统初始化中" subTitle="正在获取设备信息，请稍候..." />
                        </div>
                    </div>
                );
            case "error":
                return (
                    <div className="center-container">
                        <div className="center-content">
                            <Result
                                status="error"
                                title="初始化失败"
                                subTitle={initError || "系统错误，请联系管理员"}
                                extra={[
                                    <Button
                                        type="primary"
                                        key="retry"
                                        onClick={() => {
                                            setInitStatus("initializing");
                                            initializeDevice();
                                        }}
                                    >
                                        重试
                                    </Button>,
                                ]}
                            />
                        </div>
                    </div>
                );
            default:
                return null;
        }
    };

    const [isPrinting, setIsPrinting] = useState(false);

    if (initStatus === "initializing" || initStatus === "error") {
        return renderInitializationScreen();
    }

    if (fieldId === "start") return <InitializationScreen />;
    if (fieldId === "") return <ErrorScreen />;

    return (
        <>
            <ConfigProvider theme={{ token: { borderRadius: 0 } }}>{contextHolder}</ConfigProvider>
            <ConfigProvider theme={{ token: { borderRadius: 2 } }}>
                <ExitModal />
                <div className="clearcache-button">
                    <Button type="primary" icon={<RedoOutlined />} onClick={() => location.reload()} />
                </div>
                <div className="refresh-button">
                    <Button type="primary" icon={<ClearOutlined />} onClick={() => { }} />
                </div>

                <div style={{ padding: "20px" }}>{barCodeImage && stepIndex === 0 && <PaymentQRCode barCodeImage={barCodeImage} />}</div>

                <div
                    style={{
                        position: "fixed",
                        width: "100%",
                        bottom: "0px",
                        padding: "20px",
                    }}
                >
                    <div
                        className="flex-row justify-evenly items-center"
                        style={{
                            padding: "20px",
                        }}
                    >
                        <div className="font_4 text_13_bottom">{fieldName}</div>
                    </div>
                </div>

                {isPrinting && (
                    <div className="printing-overlay">
                        <div className="printing-modal">
                            <div className="printing-spinner"></div>
                            <div className="printing-text">正在打印...</div>
                        </div>
                    </div>
                )}

                {showCarSelector && (
                    <CarSelector
                        visible={showCarSelector}
                        onClose={() => {
                            setShowCarSelector(false);
                            setIsProcessing(false);
                        }}
                        onSelect={(carId) => {
                            setSelectedCarId(carId);
                            setShowCarSelector(false);
                            startCountdown(20);
                        }}
                        messageApi={messageApi}
                    />
                )}

                {countdown > 0 && (
                    <Modal title="请扫描二维码" open={true} footer={null} closable={true} centered maskClosable={false} onCancel={handleExitCountdown}>
                        <div style={{ textAlign: "center", padding: "20px" }}>
                            <div style={{ fontSize: "24px", marginBottom: "16px" }}>请在{countdown}秒内完成扫码</div>
                            <div style={{ fontSize: "16px", color: "#666" }}>超时后需要重新扫工牌</div>
                        </div>
                    </Modal>
                )}
            </ConfigProvider>
        </>
    );
};

export default Cashier;
