import React, { useState } from 'react';
import { Button, Modal, Form, Input, message, Space, Typography } from 'antd';
import { LinkOutlined } from '@ant-design/icons';
import request from '@/utils/request';

const { Text } = Typography;

interface ApplyLinkButtonProps {
  tableName: string;
  onSuccess?: () => void;
}

const ApplyLinkButton: React.FC<ApplyLinkButtonProps> = ({ tableName, onSuccess }) => {
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  const [generatedLink, setGeneratedLink] = useState<string>('');
  const [messageApi, contextHolder] = message.useMessage();

  const showModal = () => {
    setVisible(true);
    setGeneratedLink('');
  };

  const handleCancel = () => {
    setVisible(false);
    form.resetFields();
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);
      
      const response = await request('/System/ShareLink/generateLink', {
        method: 'POST',
        data: {
          tableName,
          expireHours: values.expireHours,
          description: values.description
        }
      });
      
      if (response.success) {
        setGeneratedLink(response.data);
        messageApi.success('链接生成成功');
        if (onSuccess) {
          onSuccess();
        }
      } else {
        messageApi.error(response.message || '链接生成失败');
      }
    } catch (error) {
      console.error('Form validation failed:', error);
    } finally {
      setLoading(false);
    }
  };

  const copyToClipboard = () => {
    if (generatedLink) {
      navigator.clipboard.writeText(generatedLink)
        .then(() => {
          messageApi.success('链接已复制到剪贴板');
        })
        .catch(() => {
          messageApi.error('复制失败，请手动复制');
        });
    }
  };

  return (
    <>
      {contextHolder}
      <Button 
        icon={<LinkOutlined />} 
        onClick={showModal}
      >
        申请链接
      </Button>
      
      <Modal
        title="申请共享链接"
        open={visible}
        onCancel={handleCancel}
        footer={[
          <Button key="cancel" onClick={handleCancel}>
            取消
          </Button>,
          <Button 
            key="submit" 
            type="primary" 
            loading={loading} 
            onClick={handleSubmit}
            disabled={!!generatedLink}
          >
            生成链接
          </Button>,
          generatedLink && (
            <Button 
              key="copy" 
              type="primary" 
              onClick={copyToClipboard}
            >
              复制链接
            </Button>
          )
        ]}
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{ expireHours: 24 }}
        >
          <Form.Item
            name="expireHours"
            label="链接有效期（小时）"
            rules={[{ required: true, message: '请输入链接有效期' }]}
          >
            <Input type="number" min={1} max={720} />
          </Form.Item>
          
          <Form.Item
            name="description"
            label="链接描述"
            rules={[{ required: true, message: '请输入链接描述' }]}
          >
            <Input.TextArea rows={3} placeholder="请输入链接用途描述" />
          </Form.Item>
          
          {generatedLink && (
            <Form.Item label="生成的链接">
              <Space direction="vertical" style={{ width: '100%' }}>
                <Text ellipsis style={{ width: '100%' }} copyable>
                  {generatedLink}
                </Text>
                <Text type="secondary">
                  此链接有效期为 {form.getFieldValue('expireHours')} 小时，请在有效期内使用
                </Text>
              </Space>
            </Form.Item>
          )}
        </Form>
      </Modal>
    </>
  );
};

export default ApplyLinkButton;
