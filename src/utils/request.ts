import { message, notification } from 'antd';
import { history } from 'umi';
// @ts-ignore
import { tool } from '@/utils/tool';
// @ts-ignore
import sysConfig from '@/config';
import axios, { AxiosResponse } from 'axios';
import qs from 'qs';
import moment from 'moment';

//登录的页面
const loginPath = '/user/login';

//获取用户信息的路径
const getUserPath = '/Auth/UserInfo/getLoginUser';

interface _codeMessage {
    [key: number]: string;
}

// 以下这些code需要重新登录
const reloadCodes = [401, 1011007, 1011008];

const codeMessage: _codeMessage = {
    200: '服务器成功返回请求的数据。',
    201: '新建或修改数据成功。',
    202: '一个请求已经进入后台排队（异步任务）。',
    204: '删除数据成功。',
    400: '发出的请求有错误，服务器没有进行新建或修改数据的操作。',
    401: '用户没有权限（令牌、用户名、密码错误）。',
    403: '用户得到授权，但是访问是被禁止的。',
    404: '发出的请求针对的是不存在的记录，服务器没有进行操作。',
    406: '请求的格式不可得。',
    410: '请求的资源被永久删除，且不会再得到的。',
    415: '访问参数页面错误，请联系管理员。',
    422: '当创建一个对象时，发生一个验证错误。',
    500: '服务器发生错误，请检查服务器。',
    502: '网关错误。',
    503: '服务暂时异常，请尝试刷新重试!',
    504: '网关超时。',
};

// token 键定义
const accessTokenKey = sysConfig.ACCESS_TOEKN_KEY;
const refreshAccessTokenKey = sysConfig.REFRESH_TOEKN_KEY;

/**
 * 异常处理程序
 */
const errorHandler = (error: { response: Response }): {} => {
    const { response } = error;
    if (response && response.status) {
        const errorText = codeMessage[response.status] || response.statusText;
        const { status, url } = response;
        // 处理参数问题
        let noParamUrl = url;
        if (url.indexOf('?') !== -1) {
            noParamUrl = url.substring(0, url.indexOf('?'));
        }
        if (status === 401) {
            if (history.location.pathname !== loginPath) {
                notification.error({
                    message: '请重新登陆!',
                });
                sessionStorage.clear();
                history.push('/user/login');
            }
        } else {
            notification.error({
                message: `请求错误 [${status}]: ${noParamUrl}`,
                description: errorText,
            });
        }
    } else {
        notification.error({
            description: '您的网络发生异常，无法连接服务器',
            message: '网络异常',
        });
        return {};
    }
    return response;
};

/**
 * 返回 当前使用的 Domain
 * @returns
 */
export const GetDomain = () => {
    if (window.location.hostname.startsWith('192.168')) {
        return `http://${window.location.hostname}:6677`;
    }
    if (window.location.hostname == 'localhost' || window.location.hostname == '127.0.0.1') {
        return 'http://localhost:5566';
    } else {
        return 'https://api.51panda.com';
    }
};

/**
 * 返回 用于 身份认证的  头部
 * @returns
 */
export const GetHeader = () => {
    let headers = {};
    let token = tool.data.get(accessTokenKey);
    if (token) {
        // @ts-ignore
        headers[sysConfig.TOKEN_NAME] = sysConfig.TOKEN_PREFIX + token;
        // options.headers[sysConfig.TOKEN_NAME] = sysConfig.TOKEN_PREFIX + token
        // 判断 accessToken 是否过期
        const jwt = decryptJWT(token);
        const exp = getJWTDate(jwt.exp);
        // token 已经过期
        if (new Date() >= exp) {
            // 获取刷新 token
            const refreshAccessToken = tool.data.get(refreshAccessTokenKey);
            // 携带刷新 token
            if (refreshAccessToken) {
                // @ts-ignore
                headers['X-' + sysConfig.TOKEN_NAME] = sysConfig.TOKEN_PREFIX + refreshAccessToken;
            }
        }
    }
    return headers;
};
/**
 * 处理登录失效的 错误
 */
export const error = () => {
    tool.data.remove(accessTokenKey);
    tool.data.remove(refreshAccessTokenKey);
    tool.data.remove('USER_INFO');
    tool.data.remove('MENU');
    tool.data.remove('PERMISSIONS');
};

//下面开始 用 axios  来封装的

// 创建 axios 实例
const service = axios.create({
    baseURL: GetDomain() + '/', // api base_url
    timeout: sysConfig.TIMEOUT, // 请求超时时间
});

/**
 *
 * @param url
 * @param value
 * @param options
 */
export const request = <T = any>(
    url: string,
    value: any = {},
    options = {
        timeout: undefined,
        errorMessage: true,
        responseType: undefined,
        header: {},
    },
): Promise<T> => {
    let data = value.data;
    let method = value.method;

    // 只有非 FormData 数据才进行格式化
    if (!(data instanceof FormData)) {
        value.data = formatDateFields(value.data);
        data = value.data;
    }

    if (method == undefined) {
        message.error({
            content: '当前代码没有设置相关的提交的Method，请联系技术人员!',
            className: 'custom-message-style',
        });
        // @ts-ignore
        return Promise.resolve({
            success: false,
            message: '当前代码没有设置相关的提交的Method，请联系技术人员!',
        });
    }
    else {
        if (value.timeout)
            options.timeout = value.timeout;
        else {
            if (value.responseType == 'blob') {
                value.timeout = 9999999;
                options.timeout = value.timeout;
            }
        }

        if (value.responseType) options.responseType = value.responseType;

        if (value.errorMessage !== undefined) options.errorMessage = value.errorMessage;

        if (method.toLowerCase() === 'post') {
            return service.post(url, data, {
                ...options,
                headers: { ...options.header }, // 保留自定义 headers
            });
        } else if (method.toLowerCase() === 'get') {
            return service.get(url, {
                params: data,
                ...options,
            });
        } else if (method.toLowerCase() === 'delete') {
            return service.delete(url, {
                data: data,
                ...options,
            });
        } else if (method.toLowerCase() === 'put') {
            return service.put(url, data, options);
        } else if (method === 'formdata') {
            // form-data表单提交的方式
            return service.post(url, data, {
                headers: {
                    ...options.header
                },
                transformRequest: [(data) => data], // 防止 axios 自动序列化 FormData
                ...options,
            });
        } else {
            // return Promise.reject(status)
            // @ts-ignore
            return Promise.resolve({
                success: false,
                message: '',
            });
        }
    }
};

// HTTP request 拦截器
service.interceptors.request.use(
    (config) => {
        const token = tool.data.get(accessTokenKey);
        if (token) {
            // @ts-ignore
            config.headers[sysConfig.TOKEN_NAME] = sysConfig.TOKEN_PREFIX + token;
            // 判断 accessToken 是否过期
            const jwt = decryptJWT(token);
            const exp = getJWTDate(jwt.exp);
            // token 已经过期
            if (new Date() >= exp) {
                // 获取刷新 token
                const refreshAccessToken = tool.data.get(refreshAccessTokenKey);
                // 携带刷新 token
                if (refreshAccessToken) {
                    // @ts-ignore
                    config.headers['X-' + sysConfig.TOKEN_NAME] =
                        sysConfig.TOKEN_PREFIX + refreshAccessToken;
                }
            }
        }
        if (!sysConfig.REQUEST_CACHE && config.method === 'get') {
            config.params = config.params || {};
            config.params._ = new Date().getTime();
        }
        // @ts-ignore
        Object.assign(config.headers, sysConfig.HEADERS);
        return config;
    },
    (error) => {

        return Promise.reject(error);
    },
);

// HTTP response 拦截器
service.interceptors.response.use(
    (response: AxiosResponse) => {
        const { code, msg, data } = response.data;

        // 检查服务器时间和本地时间的差异
        const serverTime = response.headers['date'] ? new Date(response.headers['date']).getTime() : null;
        const localTime = new Date().getTime();

        if (serverTime) {
            const timeDiff = Math.abs(serverTime - localTime);
            // 如果时间差异超过5分钟（300000毫秒）
            if (timeDiff > 300000) {
                message.error({
                    content: `本地时间与服务器时间差异过大（${Math.round(timeDiff / 1000 / 60)}分钟），请校准本地时间后重试`,
                    className: 'custom-message-style',
                });
                return Promise.reject({
                    success: false,
                    message: '时间同步错误'
                });
            }
        }

        // 配置了blob，不处理直接返回文件流
        if (response.config.responseType === 'blob') {
            if (response.status === 200) {
                if (response.data.type === 'application/json') {
                    let reader = new FileReader();
                    reader.readAsText(response.data, 'utf-8');
                    reader.onload = function () {
                        let json = JSON.parse(reader.result as string);

                        message.error({
                            content: json.message,
                            className: 'custom-message-style',
                        })
                    };
                    return undefined;
                } else {
                    return response;
                }
            } else {
                message.warning({
                    content: '文件下载失败或此文件不存在',
                    className: 'custom-message-style',
                });
                return undefined;
            }
        }
        // 检查并存储授权信息
        checkAndStoreAuthentication(response);

        // 处理401状态码，直接跳转登录页面
        if (code === 401) {
            clearAccessTokens();
            if (history.location.pathname !== loginPath) {
                history.push(loginPath);
            }
            return Promise.reject({
                success: false,
                message: '请重新登录'
            });
        }

        if (reloadCodes.includes(code)) {
            if (response.config.url !== getUserPath) error();
            return;
        }
        const config = response.config;

        if (code !== 200) {
            if (code === 400) {
                // @ts-ignore
                if (config.errorMessage) {
                    response.data &&
                        response.data.message &&
                        message.error({
                            content: response.data.message,
                            className: 'custom-message-style',
                        })
                }
            } else {
                // @ts-ignore
                if (config.errorMessage) {
                    response.data &&
                        response.data.message &&
                        message.error({
                            content: response.data.message,
                            className: 'custom-message-style',
                        })
                }
            }
            return Promise.resolve(response.data);
        } else {
            return Promise.resolve(clearGuid(response.data));
        }
    },
    (error) => {
        if (error) {
            // 检查是否是时间同步错误
            if (error.message === '时间同步错误') {
                return Promise.resolve({
                    success: false,
                    message: error.message,
                });
            }

            const status = 503;
            const description = codeMessage[status];

            message.error({
                content: description,
                className: 'custom-message-style',
            });
            return Promise.resolve({
                success: false,
                message: description,
            });
        } else {
            return Promise.resolve({
                success: false,
                message: '未知错误发生，刷新页面重试',
            });
        }
    },
);

// const clearGuid = (obj: any) => {
//     for (var key in obj) {
//         if (typeof obj[key] === 'object') {
//             obj[key] = clearGuid(obj[key]); // 递归调用遍历嵌套的对象
//         } else {
//             if (obj[key] == '00000000-0000-0000-0000-000000000000') {
//                 obj[key] = undefined;
//             }
//         }
//     }
//     return obj;
// };

const clearGuid = (obj: any): any => {
    if (Array.isArray(obj)) {
        return obj.map(item => clearGuid(item));
    }

    if (obj && typeof obj === 'object' && !(obj instanceof Date)) {
        for (let key in obj) {
            if (Object.prototype.hasOwnProperty.call(obj, key)) {
                obj[key] = clearGuid(obj[key]);
            }
        }
        return obj;
    }

    if (obj === '00000000-0000-0000-0000-000000000000') {
        return undefined;
    }

    return obj;
};

// 清除 token
const clearAccessTokens = () => {
    tool.data.remove(accessTokenKey);
    tool.data.remove(refreshAccessTokenKey);
};
/**
 * 将日期格式化为本地时区的字符串
 * @param date Date对象或moment对象
 * @returns 格式化后的日期字符串
 */
const formatDateToLocalString = (date: any): string => {
    if (moment.isMoment(date)) {
        // 如果是moment对象，先转换为JS Date对象
        date = date.toDate();
    }

    // 获取本地时间并格式化
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    const milliseconds = String(date.getMilliseconds()).padStart(3, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}.${milliseconds}`;
};

const formatDateFields = (values: any): any => {
    // 如果是数组，处理数组中的每个元素
    if (Array.isArray(values)) {
        return values.map(item => formatDateFields(item));
    }

    // 如果是对象，处理对象的每个属性
    if (values && typeof values === 'object' && !(values instanceof Date)) {
        const formattedValues = { ...values };

        Object.keys(formattedValues).forEach((key) => {
            const value = formattedValues[key];

            // 如果值是 null，转换为 undefined
            if (value === null) {
                formattedValues[key] = undefined;
                return;
            }

            // 如果值是数组，递归处理数组
            if (Array.isArray(value)) {
                formattedValues[key] = value.map(item => {
                    // 如果数组元素是 moment 对象
                    if (moment.isMoment(item)) {
                        // 转换为Date对象并格式化为本地时间
                        return formatDateToLocalString(item.toDate());
                    }
                    // 如果数组元素是Date对象
                    if (item instanceof Date) {
                        return formatDateToLocalString(item);
                    }
                    // 如果数组元素是对象，递归处理
                    if (item && typeof item === 'object') {
                        return formatDateFields(item);
                    }
                    return item;
                });
            }
            // 如果值是 moment 对象
            else if (moment.isMoment(value)) {
                // 转换为Date对象并格式化为本地时间
                formattedValues[key] = formatDateToLocalString(value.toDate());
            }
            // 如果值是Date对象
            else if (value instanceof Date) {
                formattedValues[key] = formatDateToLocalString(value);
            }
            // 如果值是普通对象，递归处理
            else if (value && typeof value === 'object') {
                formattedValues[key] = formatDateFields(value);
            }
        });

        return formattedValues;
    }

    // 如果是 moment 对象
    if (moment.isMoment(values)) {
        // 转换为Date对象并格式化为本地时间
        return formatDateToLocalString(values.toDate());
    }

    // 如果是Date对象
    if (values instanceof Date) {
        return formatDateToLocalString(values);
    }

    return values;
};
/**
 * 检查并存储授权信息
 * @param res 响应对象
 */
export const checkAndStoreAuthentication = (res: any) => {
    // 读取响应报文头 token 信息
    var accessToken = res.headers[accessTokenKey];
    var refreshAccessToken = res.headers[refreshAccessTokenKey];

    // 判断是否是无效 token
    if (accessToken === 'invalid_token') {
        tool.data.clear();
        //clearAccessTokens()
    }
    // 判断是否存在刷新 token，如果存在则存储在本地
    else if (refreshAccessToken && accessToken && accessToken !== 'invalid_token') {
        tool.data.clear();
        tool.data.set(accessTokenKey, accessToken);
        tool.data.set(refreshAccessTokenKey, refreshAccessToken);
    }
};
/**
 * 解密 JWT token 的信息
 * @param token jwt token 字符串
 * @returns <any>object
 */
export const decryptJWT = (token: string) => {
    token = token.replace(/_/g, '/').replace(/-/g, '+');
    var json = decodeURIComponent(escape(window.atob(token.split('.')[1])));
    return JSON.parse(json);
};

/**
 * 将 JWT 时间戳转换成 Date
 * @description 主要针对 `exp`，`iat`，`nbf`
 * @param timestamp 时间戳
 * @returns Date 对象
 */
export const getJWTDate = (timestamp: number) => {
    return new Date(timestamp * 1000);
};

export default request;
