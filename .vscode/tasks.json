{
    // See https://go.microsoft.com/fwlink/?LinkId=733558
    // for the documentation about the tasks.json format
    "version": "2.0.0",
    "tasks": [
        {
            "label": "PandaWeb Publish",
            "type": "shell",
            "command": "yarn",
            "args": [
                "build"
            ],
            "group": {
                "kind": "build",
                "isDefault": true
            },
            "presentation": {
                "reveal": "always",
                "panel": "dedicated"
            },
            "problemMatcher": []
        },
        {
            "label": "Git Push Daily",
            "type": "shell",
            "command": "git",
            "args": [
                "add",
                ".",
                "&&",
                "git",
                "commit",
                "--no-verify",
                "-m",
                "daily",
                "&&",
                "git",
                "push",
                "-u",
                "origin",
                "master",
                "-f"
            ],
            "group": {
                "kind": "build",
                "isDefault": true
            },
            "problemMatcher": []
        }
    ]
}
