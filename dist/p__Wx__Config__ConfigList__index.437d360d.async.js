"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[2375],{3224:function(j,i,e){e.r(i);var c=e(15009),u=e.n(c),E=e(99289),P=e.n(E),M=e(5574),m=e.n(M),C=e(89545),I=e(51477),g=e(42509),W=e(11774),b=e(45360),h=e(83622),r=e(67294),D=e(78158),a=e(85893),O=function(){var T=b.ZP.useMessage(),p=m()(T,2),S=p[0],x=p[1],y=(0,r.useRef)(),l=(0,r.useRef)(),v=(0,r.useState)(),f=m()(v,2),d=f[0],A=f[1],R=function(n){return(0,a.jsx)("a",{onClick:function(){l.current.get(n.Id)},title:"\u7F16\u8F91",children:n.Name},"edit")},B=[{dataIndex:"RowIndex",title:"\u5E8F\u53F7",fixed:"left",align:"center"},{dataIndex:"Name",title:"\u5FAE\u4FE1\u540D\u79F0",align:"center",render:function(n,s){return R(s)}},{dataIndex:"BindTenantName",title:"\u7ED1\u5B9A",align:"center"},{dataIndex:"AppId",title:"AppId",align:"center"},{dataIndex:"PrincipalName",title:"\u516C\u53F8\u4E3B\u4F53",align:"center"},{dataIndex:"Id",title:"\u5220\u9664",fixed:"left",align:"center",deleteColumn:!0,deletePath:"/Wx/WxConfig/{Id}",deleteMessage:"\u786E\u8BA4\u5220\u9664\u8BE5\u914D\u7F6E?"}],L=[(0,a.jsx)(h.ZP,{icon:(0,a.jsx)(g.Z,{}),type:"primary",onClick:function(){return l.current.get(void 0)},children:"\u6DFB\u52A0\u914D\u7F6E"},"add")];return(0,a.jsxs)(W._z,{header:{breadcrumb:{},title:""},children:[x,(0,a.jsx)(I.Z,{ref:y,formCols:[],columns:B,tableButtons:L,getPath:"/Wx/WxConfig/GetWxConfigList",rowKey:"Id"},"wx-config-list-table"),(0,a.jsx)(C.default,{ref:l,formItems:[{name:"Id",type:"hidden",label:"",placeholder:"",required:!1},{name:"Name",type:"input",label:"\u5E94\u7528\u540D\u79F0",placeholder:"\u8BF7\u8F93\u5165\u5E94\u7528\u540D\u79F0",required:!0},{name:"PrincipalName",type:"input",label:"\u4E3B\u4F53\u540D\u79F0",placeholder:"\u8BF7\u8F93\u5165\u4E3B\u4F53\u540D\u79F0",required:!0},{name:"AppId",type:"input",label:"\u5E94\u7528\u7F16\u53F7",placeholder:"\u8BF7\u8F93\u5165\u5E94\u7528\u7F16\u53F7 AppId",required:!0},{name:"AppSecret",type:"input",label:"\u5E94\u7528\u79D8\u94A5",placeholder:"\u8BF7\u8F93\u5165\u5E94\u7528\u79D8\u94A5 AppSecret",required:!0},{name:"Token",type:"input",label:"\u52A0\u5BC6\u51ED\u636E",placeholder:"\u8BF7\u8F93\u5165Token"},{name:"EncodingAESKey",type:"input",label:"\u52A0\u5BC6\u79D8\u94A5",placeholder:"\u8BF7\u8F93\u5165\u5E94\u7528\u52A0\u5BC6\u79D8\u94A5"},{name:"IsWxApp",type:"switch",label:"\u662F\u5C0F\u7A0B\u5E8F"},{name:"BindTenantId",type:"select",label:"\u7ED1\u5B9A\u8D26\u6237",placeholder:"\u8BF7\u9009\u62E9\u7ED1\u5B9A\u8D26\u6237",search:!0,request:function(){var _=P()(u()().mark(function s(U){var o;return u()().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,(0,D.ZP)("/Tenant/getTenantSelectList",{method:"POST",data:{keyWords:U.keyWords,TenantId:d==null?void 0:d.BindTenantId}});case 2:if(o=t.sent,!o.success){t.next=5;break}return t.abrupt("return",o.data||[]);case 5:return t.abrupt("return",[]);case 6:case"end":return t.stop()}},s)}));function n(s){return _.apply(this,arguments)}return n}()}],getPath:"/Wx/WxConfig/getWxConfigInfo",setPath:"/Wx/WxConfig/setWxConfigInfo",modifyTitle:"\u4FEE\u6539\u914D\u7F6E",insertTitle:"\u6DFB\u52A0\u914D\u7F6E",onLoad:function(n){console.log("record"),console.log(n),A(n)}})]})};i.default=O}}]);
