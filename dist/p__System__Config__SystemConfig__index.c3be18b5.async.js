"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[1689],{90672:function(F,h,e){var D=e(1413),R=e(91),b=e(67294),r=e(31649),y=e(85893),l=["fieldProps","proFieldProps"],I=function(g,C){var T=g.fieldProps,K=g.proFieldProps,U=(0,R.Z)(g,l);return(0,y.jsx)(r.Z,(0,D.Z)({ref:C,valueType:"textarea",fieldProps:T,proFieldProps:K},U))};h.Z=b.forwardRef(I)},37101:function(F,h,e){e.r(h);var D=e(97857),R=e.n(D),b=e(15009),r=e.n(b),y=e(99289),l=e.n(y),I=e(5574),m=e.n(I),g=e(18023),C=e(78158),T=e(63783),K=e(11774),U=e(37476),v=e(5966),Z=e(90672),$=e(88280),w=e(45360),V=e(85576),E=e(67294),s=e(85893),k=function(){var z=w.ZP.useMessage(),A=m()(z,2),M=A[0],G=A[1],O=E.useRef(),H=E.useState(),j=m()(H,2),J=j[0],N=j[1],Q=E.useState(),S=m()(Q,2),Y=S[0],X=S[1],q=E.useRef(),ee=E.useState(!1),x=m()(ee,2),ne=x[0],B=x[1],ae=E.useState(),L=m()(ae,2),te=L[0],se=L[1];return(0,s.jsxs)(s.Fragment,{children:[G,(0,s.jsxs)(K._z,{header:{breadcrumb:{},title:""},children:[(0,s.jsxs)(U.Y,{onFinish:function(){var u=l()(r()().mark(function i(n){return r()().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,(0,C.ZP)("/Config/Config/setSysConfig",{method:"PUT",data:n}).then(function(a){var o;(o=O.current)===null||o===void 0||o.reload(),a&&a.success&&M.success(a.message)});case 2:case"end":return t.stop()}},i)}));return function(i){return u.apply(this,arguments)}}(),formRef:q,open:ne,onOpenChange:B,autoFocusFirstInput:!0,modalProps:{destroyOnClose:!0},initialValues:te,title:"\u7F16\u8F91\u914D\u7F6E",className:"no-padding",width:830,children:[(0,s.jsx)(v.Z,{hidden:!0,name:"Id"}),(0,s.jsx)(v.Z,{hidden:!0,name:"ConfigKey"}),(0,s.jsx)(v.Z,{hidden:!0,name:"SortCode"}),(0,s.jsx)(v.Z,{hidden:!0,name:"Remark"}),(0,s.jsx)(Z.Z,{name:"ConfigValue",label:"",placeholder:"\u8BF7\u8F93\u5165\u914D\u7F6E\u503C",fieldProps:{style:{height:400}}})]}),(0,s.jsx)($.Z,{actionRef:O,rowKey:"Id",recordCreatorProps:{position:"bottom",record:function(){return{Id:"00000000-0000-0000-0000-000000000000",ConfigKey:void 0,ConfigValue:void 0,Category:void 0,Remark:void 0,SortCode:0}}},headerTitle:"\u7CFB\u7EDF\u914D\u7F6E",search:!1,scroll:{x:"1500"},columns:[{title:"\u914D\u7F6E\u952E",dataIndex:"ConfigKey",align:"center",request:function(){var u=l()(r()().mark(function n(){var _;return r()().wrap(function(a){for(;;)switch(a.prev=a.next){case 0:return a.next=2,(0,g.vq)();case 2:if(_=a.sent,!_.success){a.next=7;break}return a.abrupt("return",_.data?_.data:[]);case 7:return a.abrupt("return",[]);case 8:case"end":return a.stop()}},n)}));function i(){return u.apply(this,arguments)}return i}(),ellipsis:!0,search:!1,width:400,render:function(i,n,_,t){return[n.ConfigKeyText==""||n.ConfigKeyText==null?n.ConfigKey:n.ConfigKeyText]}},{title:"\u914D\u7F6E\u503C",dataIndex:"ConfigValue",align:"center",ellipsis:!0,search:!1,width:300,render:function(i,n,_,t){return[(0,s.jsx)("a",{onClick:function(){se({Id:n.Id,ConfigKey:n.ConfigKey,ConfigValue:n.ConfigValue,SortCode:n.SortCode,Remark:n.Remark}),B(!0)},children:n.ConfigValue})]}},{title:"\u6392\u5E8F",dataIndex:"SortCode",align:"center",ellipsis:!0,search:!1,width:100},{title:"\u5907\u6CE8",dataIndex:"Remark",align:"center",ellipsis:!0,search:!1,width:100},{title:"\u64CD\u4F5C",width:150,valueType:"option",align:"center",search:!1,render:function(i,n,_,t){return[(0,s.jsxs)("div",{style:{textAlign:"center",width:"100%"},children:[(0,s.jsx)("a",{onClick:function(){var o;t==null||(o=t.startEditable)===null||o===void 0||o.call(t,n.Id)},children:"\u7F16\u8F91"},"editable"),(0,s.jsx)("a",{style:{marginLeft:"10px"},onClick:function(){V.Z.confirm({title:"\u786E\u8BA4\u64CD\u4F5C",icon:(0,s.jsx)(T.Z,{}),content:"\u662F\u5426\u786E\u8BA4\u5220\u9664\u8BE5\u6761\u914D\u7F6E?",okText:"\u786E\u8BA4",cancelText:"\u53D6\u6D88",onOk:function(){var o=l()(r()().mark(function f(){return r()().wrap(function(c){for(;;)switch(c.prev=c.next){case 0:return c.next=2,(0,C.ZP)("/Config/Config/deleteSysConfig",{method:"DELETE",data:{Id:n.Id}}).then(function(P){if(P&&P.success){var W;M.success(P.message),(W=O.current)===null||W===void 0||W.reload()}else P&&M.error(P.message)});case 2:case"end":return c.stop()}},f)}));function d(){return o.apply(this,arguments)}return d}(),onCancel:function(){var o=l()(r()().mark(function f(){return r()().wrap(function(c){for(;;)switch(c.prev=c.next){case 0:case"end":return c.stop()}},f)}));function d(){return o.apply(this,arguments)}return d}()})},children:"\u5220\u9664"},"Enabled")]},"editable-div")]}}],pagination:{showSizeChanger:!0,showQuickJumper:!0,defaultPageSize:10},request:l()(r()().mark(function u(){var i,n,_=arguments;return r()().wrap(function(a){for(;;)switch(a.prev=a.next){case 0:return i=_.length>0&&_[0]!==void 0?_[0]:{},a.next=3,(0,C.ZP)("/Config/Config/getConfigList",{method:"POST",data:R()({},i)});case 3:return n=a.sent,a.abrupt("return",{success:n.success,data:n.data.data,total:n.data.total});case 5:case"end":return a.stop()}},u)})),value:Y,onChange:X,editable:{type:"multiple",editableKeys:J,onSave:function(){var u=l()(r()().mark(function n(_,t,a){return r()().wrap(function(d){for(;;)switch(d.prev=d.next){case 0:return d.next=2,(0,C.ZP)("/Config/Config/setSysConfig",{method:"PUT",data:t}).then(function(f){var p;(p=O.current)===null||p===void 0||p.reload(),f&&f.success&&M.success(f.message)});case 2:case"end":return d.stop()}},n)}));function i(n,_,t){return u.apply(this,arguments)}return i}(),onChange:N}})]})]})};h.default=k}}]);
