"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[1192],{16894:function(C,t,e){var l=e(17014);t.ZP=l.Z},92676:function(C,t,e){e.r(t);var l=e(15009),s=e.n(l),g=e(99289),i=e.n(g),h=e(5574),c=e.n(h),m=e(78158),I=e(63783),T=e(11774),f=e(16894),v=e(45360),b=e(85576),E=e(67294),n=e(85893),A=function(){var R=v.ZP.useMessage(),M=c()(R,2),p=M[0],U=M[1],W=E.useRef(),B=E.useState(-1),P=c()(B,2),L=P[0],K=P[1];return(0,n.jsxs)(n.Fragment,{children:[U,(0,n.jsx)(T._z,{header:{breadcrumb:{},title:""},children:(0,n.jsx)(f.ZP,{cardBordered:!0,scroll:{x:"max-content"},actionRef:W,columns:[{dataIndex:"key",title:"\u5E8F\u53F7",width:50,align:"center"},{dataIndex:"name",title:"\u540D\u79F0",align:"center"},{dataIndex:"ParentNames",title:"\u8DEF\u5F84",align:"center"},{dataIndex:"ParentIds",title:"\u8DEF\u5F84 \u7F16\u53F7",align:"center"},{title:"\u64CD\u4F5C",valueType:"option",align:"center",render:function(u,a){return[(0,n.jsx)("div",{style:{width:"100%",textAlign:"center"},children:(0,n.jsx)("a",{onClick:function(){b.Z.confirm({title:"\u786E\u8BA4\u64CD\u4F5C",icon:(0,n.jsx)(I.Z,{}),content:"\u662F\u5426\u786E\u8BA4\u5C06\u6B64\u83DC\u5355\u8D4B\u7ED9\u4ED6\u7684\u4E0A\u7EA7\u83DC\u5355\u62E5\u6709\u7684\u516C\u53F8?",okText:"\u786E\u8BA4",cancelText:"\u53D6\u6D88",onOk:function(){var O=i()(s()().mark(function j(){return s()().wrap(function(d){for(;;)switch(d.prev=d.next){case 0:return console.log("record"),console.log(a),d.next=4,(0,m.ZP)("/Auth/Menu/TenantMenu/makeChildredMenu",{method:"POST",data:{Id:a.key}}).then(function(o){o&&o.success&&p.success(o.message)});case 4:case"end":return d.stop()}},j)}));function _(){return O.apply(this,arguments)}return _}()})},children:"\u521D\u59CB\u516C\u53F8\u6743\u9650"},"select")})]}},{dataIndex:"EnCode",title:"\u5168\u5C40\u7F16\u53F7",align:"center"},{dataIndex:"sort",title:"\u6392\u5E8F",align:"center"},{dataIndex:"path",title:"\u5730\u5740",align:"center"}],request:i()(s()().mark(function D(){var u,a,r=arguments;return s()().wrap(function(_){for(;;)switch(_.prev=_.next){case 0:return u=r.length>0&&r[0]!==void 0?r[0]:{},u.ParentId=L,_.next=4,(0,m.ZP)("/Auth/Menu/getMenuList",{method:"POST",data:u,errorMessage:!1});case 4:return a=_.sent,_.abrupt("return",{success:a.success,data:a.data.data,total:a.data.total});case 6:case"end":return _.stop()}},D)})),rowKey:"key",search:!1,headerTitle:"\u83DC\u5355\u5217\u8868"},"key")})]})};t.default=A}}]);
