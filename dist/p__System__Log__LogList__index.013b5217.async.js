"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[1990],{12620:function(g,s,e){e.r(s);var l=e(5574),o=e.n(l),r=e(67294),i=e(45360),u=e(11774),m=e(51477),E=e(98820),t=e(85893),I=function(){var M=i.ZP.useMessage(),_=o()(M,2),P=_[0],f=_[1],n=r.useRef(null);return(0,t.jsxs)(t.Fragment,{children:[f,(0,t.jsxs)(u._z,{header:{breadcrumb:{},title:""},children:[(0,t.jsx)(m.Z,{getPath:"/SystemManage/Log/getLogList",rowKey:"Id",columns:[{title:"\u64CD\u4F5C\u4EBA",dataIndex:"CreateUserName"},{title:"\u521B\u5EFA\u65F6\u95F4",dataIndex:"CreateTime",IsTime:!0},{title:"\u5B66\u5458\u59D3\u540D",dataIndex:"xm",render:function(L,d){return(0,t.jsx)("a",{onClick:function(){var a;n==null||(a=n.current)===null||a===void 0||a.GetStudentInfo(d.StudentId)},children:d.xm},"xm")}},{title:"\u8BC1\u4EF6\u53F7",dataIndex:"sfzmhm"},{title:"IP\u5730\u5740",dataIndex:"Ip"},{title:"\u65E5\u5FD7\u5185\u5BB9",dataIndex:"Log",align:"left"}],formCols:[{type:"selectuser",name:"CreateUserId",label:"\u64CD\u4F5C\u4EBA",width:"100%",placeholder:"\u8BF7\u9009\u62E9\u64CD\u4F5C\u4EBA"},{type:"datetimerange",name:"CreateTimes",label:"\u65F6\u95F4\u8303\u56F4",width:"100%"},{type:"input",name:"SearchKey",label:"\u5173\u952E\u5B57\u8BCD",width:"100%",placeholder:"\u8BF7\u8F93\u5165\u5173\u952E\u5B57\u8BCD"}]}),(0,t.jsx)(E.Z,{ref:n,StudentListRef:void 0,updateAddLoading:void 0})]})]})};s.default=I}}]);
