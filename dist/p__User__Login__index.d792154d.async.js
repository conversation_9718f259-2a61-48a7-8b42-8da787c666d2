"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[9366],{85671:function(ct,Y,r){r.r(Y),r.d(Y,{default:function(){return Ke}});var Ce=r(15009),c=r.n(Ce),xe=r(97857),v=r.n(xe),ye=r(99289),F=r.n(ye),be=r(5574),y=r.n(be),Se=r(2618),H=r(78158),we=r(75594),P=r(32273),_=r(45360),je=r(21532),Fe=r(56595),Pe=r(80688),x=r(67294),Le=r(73935),Ie=r(34257),Be=r(37476),Z=r(5966),Me=r(31199),e=r(85893),Te=function(a){var d=a.visible,t=a.onVisibleChange,f=a.onFinish,n=a.changePwdAccountList;return(0,e.jsxs)(Be.Y,{onFinish:f,open:d,onOpenChange:t,modalProps:{destroyOnClose:!0},title:"\u91CD\u7F6E\u5BC6\u7801",width:400,children:[(0,e.jsx)(Z.Z,{name:"TenantId",hidden:!0,initialValue:"1F79F34F-7EA8-4203-8202-7C8C6E742115"}),(0,e.jsx)(Me.Z,{name:"Phone",label:"\u7535\u8BDD\u53F7\u7801",placeholder:"\u8BF7\u8F93\u5165\u8054\u7CFB\u4EBA\u7684\u7535\u8BDD\u53F7\u7801",fieldProps:{maxLength:11},rules:[{required:!0,message:"\u8BF7\u8F93\u5165\u8054\u7CFB\u4EBA\u7684\u7535\u8BDD\u53F7\u7801!"}]}),(0,e.jsx)(Z.Z.Password,{name:"PassWord",label:"\u767B\u5F55\u5BC6\u7801",placeholder:"\u8BF7\u8F93\u5165\u767B\u5F55\u5BC6\u7801"}),(0,e.jsx)(Z.Z.Password,{name:"PassWord2",label:"\u786E\u8BA4\u5BC6\u7801",placeholder:"\u8BF7\u518D\u6B21\u8F93\u5165\u5BC6\u7801"})]})},ee=Te,te=r(40056),Ae=function(a){var d=a.content;return(0,e.jsx)(te.Z,{style:{marginBottom:24},message:d,type:"error",showIcon:!0})},Ee=Ae,Re=r(64317),He=function(a){var d=a.accountList,t=a.onAccountListClear;return d.length<=1?null:(0,e.jsx)(Re.Z,{params:{accountList:d},placeholder:"\u8BF7\u9009\u62E9\u8981\u767B\u5F55\u7684\u8D26\u53F7",fieldProps:{size:"large",allowClear:!0,onClear:function(){console.log("\u8D26\u53F7\u9009\u62E9\u5DF2\u6E05\u9664"),t==null||t()}},initialValue:void 0,request:F()(c()().mark(function f(){return c()().wrap(function(i){for(;;)switch(i.prev=i.next){case 0:return i.abrupt("return",d);case 1:case"end":return i.stop()}},f)})),name:"userId"})},ke=r(29177),De=r(57308),Ze=function(a){var d=a.accountList,t=a.intl;return(0,e.jsxs)(e.Fragment,{children:[d.length===0&&(0,e.jsx)(Z.Z,{name:"account",fieldProps:{size:"large",prefix:(0,e.jsx)(ke.Z,{}),style:{textTransform:"uppercase"},autoCapitalize:"characters",inputMode:"text",onChange:function(n){var i=n.target.value.toUpperCase();n.target.value=i},onKeyPress:function(n){n.target.value=n.target.value.toUpperCase()},onInput:function(n){n.target.value=n.target.value.toUpperCase()},onCompositionStart:function(n){n.target.style.imeMode="disabled"},onCompositionEnd:function(n){n.target.value=n.target.value.toUpperCase()}},placeholder:t.formatMessage({id:"pages.login.account.placeholder",defaultMessage:"\u7528\u6237\u540D"}),rules:[{required:!0,message:(0,e.jsx)(P.FormattedMessage,{id:"pages.login.account.required",defaultMessage:"\u8BF7\u8F93\u5165\u7528\u6237\u540D!"})}]}),(0,e.jsx)(Z.Z.Password,{name:"password",fieldProps:{size:"large",prefix:(0,e.jsx)(De.Z,{})},placeholder:t.formatMessage({id:"pages.login.password.placeholder",defaultMessage:"\u5BC6\u7801"}),rules:[{required:!0,message:(0,e.jsx)(P.FormattedMessage,{id:"pages.login.password.required",defaultMessage:"\u8BF7\u8F93\u5165\u5BC6\u7801\uFF01"})}]})]})},q=r(88634),$e=r(89545),Qe=function(a){var d=a.visible,t=a.onVisibleChange,f=_.ZP.useMessage(),n=y()(f,2),i=n[0],E=n[1],B=x.useRef(),M=(0,x.useState)([]),k=y()(M,2),$=k[0],U=k[1],V=[{name:"Id",type:"hidden"},{name:"TenantId",type:"hidden",value:"1F79F34F-7EA8-4203-8202-7C8C6E742115"},{name:"Code",type:"input",label:"\u6CE8\u518C\u4EE3\u7801",placeholder:"\u8BF7\u586B\u5199\u5DE5\u4F5C\u4EBA\u5458\u63D0\u4F9B\u7684\u6CE8\u518C\u4EE3\u7801",required:!0},{name:"FullName",type:"input",label:"\u9A7E\u6821\u5168\u79F0",placeholder:"\u8BF7\u8F93\u5165\u9A7E\u6821\u5DE5\u5546\u767B\u8BB0\u7684\u516C\u53F8\u5168\u79F0",required:!0},{name:"CoachPay",type:"switch",label:"\u6559\u7EC3\u7F34\u8D39"},{name:"",type:"group",children:[{name:"BusNum",type:"number",label:"\u5BA2\u8F66\u6570\u91CF",placeholder:"\u8BF7\u8F93\u5165\u5BA2\u8F66\u6570\u91CF",required:!0,width:150},{name:"TractorNum",type:"number",label:"\u7275\u5F15\u8F66\u6570",placeholder:"\u8BF7\u8F93\u5165\u7275\u5F15\u8F66\u6570",required:!0,width:150},{name:"TruckNum",type:"number",label:"\u8D27\u8F66\u6570\u91CF",placeholder:"\u8BF7\u8F93\u5165\u8D27\u8F66\u6570\u91CF",required:!0,width:150}]},{name:"",type:"group",children:[{name:"CarNum",type:"number",label:"\u5C0F\u8F66\u6570\u91CF",placeholder:"\u8BF7\u8F93\u5165\u5C0F\u8F66\u6570\u91CF",required:!0,width:150},{name:"OtherNum",type:"number",label:"\u5176\u4ED6\u6570\u91CF",placeholder:"\u8BF7\u8F93\u5165\u5176\u4ED6\u6570\u91CF",required:!0,width:150},{name:"CheckedCarNum",type:"number",label:"\u6838\u5B9A\u6570\u91CF",placeholder:"\u8BF7\u8F93\u5165\u6838\u5B9A\u6570\u91CF",required:!0,width:150}]},{name:"",type:"group",children:[{name:"TheoryTrainersNum",type:"number",label:"\u7406\u8BBA\u6559\u5458",placeholder:"\u8BF7\u8F93\u5165\u7406\u8BBA\u6559\u5458",required:!0,width:150},{name:"OperationTrainersNum",type:"number",label:"\u5B9E\u64CD\u6559\u5458",placeholder:"\u8BF7\u8F93\u5165\u5B9E\u64CD\u6559\u5458",required:!0,width:150},{name:"QualificationRecord",type:"select",label:"\u8D44\u8D28\u7B49\u7EA7",placeholder:"\u9009\u62E9\u8D44\u8D28\u7B49\u7EA7",required:!0,width:150,options:[{label:"\u4E00\u7C7B",value:"\u4E00\u7C7B"},{label:"\u4E8C\u7C7B",value:"\u4E8C\u7C7B"},{label:"\u4E09\u7C7B",value:"\u4E09\u7C7B"}]}]},{name:"",type:"group",children:[{name:"LegalPerson",type:"input",label:"\u6CD5\u4EBA\u59D3\u540D",placeholder:"\u8BF7\u8F93\u5165\u6CD5\u4EBA\u59D3\u540D",required:!0,width:"sm"},{name:"LegalPersonPhone",type:"input",label:"\u8054\u7CFB\u65B9\u5F0F",placeholder:"\u8BF7\u8F93\u5165\u6CD5\u4EBA\u8054\u7CFB\u65B9\u5F0F",required:!0,width:"md",maxLength:11,showCount:!0}]},{name:"",type:"group",children:[{name:"ContactPerson",type:"input",label:"\u8054\u7CFB\u4EBA\u540D",placeholder:"\u8BF7\u8F93\u5165\u8054\u7CFB\u4EBA\u540D",required:!0,width:"sm"},{name:"ContactPersonPhone",type:"input",label:"\u8054\u7CFB\u65B9\u5F0F",placeholder:"\u8BF7\u8F93\u5165\u8054\u7CFB\u4EBA\u8054\u7CFB\u65B9\u5F0F",required:!0,width:"md",maxLength:11,showCount:!0}]},{name:"",type:"group",children:[{name:"ProvinceId",type:"select",label:"\u6240\u5728\u7701\u4EFD",width:107,required:!0,request:q.Bq,value:43e4},{name:"CityId",type:"select",label:"\u6240\u5728\u57CE\u5E02",placeholder:"\u9009\u62E9\u9A7E\u6821\u6240\u5728\u57CE\u5E02",required:!0,width:200,request:function(){var T=F()(c()().mark(function A(){var b;return c()().wrap(function(l){for(;;)switch(l.prev=l.next){case 0:return l.next=2,(0,q.vz)(43e4);case 2:return b=l.sent,l.abrupt("return",b.success?b.data:[]);case 4:case"end":return l.stop()}},A)}));function S(){return T.apply(this,arguments)}return S}(),onChange:function(){var T=F()(c()().mark(function A(b){var D,l;return c()().wrap(function(R){for(;;)switch(R.prev=R.next){case 0:return(D=B.current)===null||D===void 0||D.setValue("AreaId",void 0),R.next=3,(0,q.RT)(b);case 3:l=R.sent,U(l.data);case 5:case"end":return R.stop()}},A)}));function S(A){return T.apply(this,arguments)}return S}()},{name:"AreaId",type:"select",label:"\u6240\u5C5E\u533A\u57DF",placeholder:"\u9009\u62E9\u9A7E\u6821\u6240\u5C5E\u533A\u57DF",required:!0,width:200,options:$}]},{name:"PostAddress",type:"input",label:"\u90AE\u5BC4\u5730\u5740",placeholder:"\u8BF7\u8F93\u5165\u90AE\u5BC4\u5730\u5740",required:!0},{name:"Remark",type:"textarea",label:"\u9A7E\u6821\u5907\u6CE8",placeholder:"\u8BF7\u8F93\u5165\u9A7E\u6821\u5907\u6CE8",height:100}],G=function(){var T=F()(c()().mark(function S(A){var b;return c()().wrap(function(l){for(;;)switch(l.prev=l.next){case 0:return l.prev=0,l.next=3,(0,H.ZP)("/HNJiaXie/HnjxCompany/setCompanyInfo",{method:"PUT",data:A});case 3:if(b=l.sent,!(b&&b.success)){l.next=11;break}return t(!1),i.success(b.message),P.history.push({pathname:"/user/login"}),l.abrupt("return",!0);case 11:return i.error(b.message),l.abrupt("return",!1);case 13:l.next=19;break;case 15:return l.prev=15,l.t0=l.catch(0),i.error("\u63D0\u4EA4\u5931\u8D25"),l.abrupt("return",!1);case 19:case"end":return l.stop()}},S,null,[[0,15]])}));return function(A){return T.apply(this,arguments)}}();return(0,e.jsxs)(e.Fragment,{children:[E,(0,e.jsx)($e.default,{ref:B,open:d,onOpenChange:t,formItems:V,onAfterOpen:function(){},insertTitle:"\u9A7E\u6821\u767B\u8BB0",modifyTitle:"\u9A7E\u6821\u767B\u8BB0",setPath:"/HNJiaXie/HnjxCompany/setCompanyInfo",getPath:"",initData:{ProvinceId:43e4},onCallBack:function(S){S&&S.success&&P.history.push({pathname:"/user/login"})},width:800})]})},Oe=Qe,ze=function(a){var d=a.isHnjxHost,t=a.showHnJxChangePwd,f=a.setShowHnJxChangePwd,n=(0,x.useState)(!1),i=y()(n,2),E=i[0],B=i[1];return(0,e.jsxs)("div",{style:{marginTop:24},children:[d&&(0,e.jsxs)(e.Fragment,{children:[(0,e.jsx)("a",{onClick:function(){f(!0)},children:"\u91CD\u7F6E\u5BC6\u7801"}),(0,e.jsx)("a",{onClick:function(){B(!0)},style:{marginLeft:20},children:"\u8D26\u6237\u6CE8\u518C"})]}),!d&&(0,e.jsx)("a",{onClick:function(){window.location.href="/user/app"},children:"\u8F6F\u4EF6\u4E0B\u8F7D"}),(0,e.jsx)(Oe,{visible:E,onVisibleChange:B})]})},Je=function(a){var d=a.status,t=a.statusMessage,f=a.accountList,n=a.loginRef,i=a.showHnJxChangePwd,E=a.setShowHnJxChangePwd,B=a.onAccountListClear,M=a.isHnjxHost,k=(0,P.useIntl)();return(0,e.jsxs)(e.Fragment,{children:[M&&(0,e.jsx)(te.Z,{style:{marginBottom:24},message:"\u5982\u6709\u7591\u95EE\uFF0C\u8BF7\u5DE5\u4F5C\u65F6\u95F4\u8054\u7CFB************\u3002",type:"info",showIcon:!0}),M&&(0,e.jsx)(Z.Z,{name:"TenantId",hidden:!0,initialValue:"1F79F34F-7EA8-4203-8202-7C8C6E742115"}),d==="error"&&(0,e.jsx)(Ee,{content:t}),(0,e.jsx)(He,{accountList:f,onAccountListClear:B}),(0,e.jsx)(Ze,{accountList:f,intl:k}),(0,e.jsx)(ze,{isHnjxHost:M,showHnJxChangePwd:i,setShowHnJxChangePwd:E})]})},W=r(43471),qe=r(28508),We=r(88284),N=r(57381),Ne=function(a){var d,t=a.qrCodeData,f=a.refreshQrCode;return(0,e.jsxs)("div",{style:{display:"flex",flexDirection:"column",alignItems:"center"},children:[(0,e.jsx)("div",{style:{width:200,height:200,backgroundColor:"#f5f5f5",display:"flex",justifyContent:"center",alignItems:"center",border:"1px solid #e8e8e8",position:"relative"},children:t.loading?(0,e.jsx)(N.Z,{tip:"\u52A0\u8F7D\u4E2D..."}):t.status==="Error"?(0,e.jsxs)("div",{style:{width:"100%",height:"100%",backgroundColor:"#f5f5f5",display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",gap:"12px"},children:[(0,e.jsx)("div",{style:{fontSize:"16px",color:"#333",fontWeight:500},children:"\u8BFB\u53D6\u4E8C\u7EF4\u7801\u5931\u8D25"}),(0,e.jsxs)("a",{onClick:f,style:{display:"flex",alignItems:"center",color:"#1890ff",cursor:"pointer",fontSize:"14px",padding:"8px 16px",border:"1px solid #1890ff",borderRadius:"4px",transition:"all 0.3s"},onMouseEnter:function(i){return i.currentTarget.style.backgroundColor="#f0f5ff"},onMouseLeave:function(i){return i.currentTarget.style.backgroundColor="transparent"},children:[(0,e.jsx)(W.Z,{style:{marginRight:"6px"}}),"\u70B9\u51FB\u5237\u65B0"]})]}):t.qrCodeImage?(0,e.jsxs)(e.Fragment,{children:[(0,e.jsx)("img",{src:"data:image/jpeg;base64,".concat(t.qrCodeImage),alt:"\u767B\u5F55\u4E8C\u7EF4\u7801",style:{width:"100%",height:"100%",objectFit:"contain",filter:t.status==="Scanned"?"grayscale(100%)":"none",opacity:t.status==="Scanned"?.5:1,transition:"all 0.3s"}}),(t.status==="Expired"||t.status==="Canceled"||t.status==="Scanned"||t.status==="Confirmed"||t.status==="Success"||t.status==="Failed")&&(0,e.jsx)("div",{style:{position:"absolute",top:0,left:0,right:0,bottom:0,backgroundColor:t.status==="Success"||t.status==="Failed"||t.status==="Confirmed"?"rgba(255, 255, 255, 0.95)":t.status==="Scanned"?"rgba(255, 255, 255, 0.8)":"rgba(255, 255, 255, 0.95)",display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",gap:"12px",transition:"all 0.3s"},children:t.status==="Failed"?(0,e.jsxs)(e.Fragment,{children:[(0,e.jsx)("div",{style:{width:"60px",height:"60px",borderRadius:"50%",backgroundColor:"#ff4d4f",display:"flex",justifyContent:"center",alignItems:"center",marginBottom:"8px"},children:(0,e.jsx)(qe.Z,{style:{fontSize:"32px",color:"#fff"}})}),(0,e.jsx)("div",{style:{fontSize:((d=t.errorMessage)===null||d===void 0?void 0:d.length)>10?"12px":"16px",color:"#ff4d4f",fontWeight:500,textAlign:"center",padding:"0 12px",wordBreak:"break-word"},children:t.errorMessage}),(0,e.jsxs)("a",{onClick:f,style:{display:"flex",alignItems:"center",color:"#1890ff",cursor:"pointer",fontSize:"14px",padding:"4px 16px",border:"1px solid #1890ff",borderRadius:"4px",marginTop:"8px",transition:"all 0.3s"},onMouseEnter:function(i){return i.currentTarget.style.backgroundColor="#f0f5ff"},onMouseLeave:function(i){return i.currentTarget.style.backgroundColor="transparent"},children:[(0,e.jsx)(W.Z,{style:{marginRight:"6px"}}),"\u91CD\u65B0\u626B\u7801"]})]}):t.status==="Success"?(0,e.jsxs)(e.Fragment,{children:[(0,e.jsx)("div",{style:{width:"60px",height:"60px",borderRadius:"50%",backgroundColor:"#52c41a",display:"flex",justifyContent:"center",alignItems:"center",marginBottom:"8px"},children:(0,e.jsx)(We.Z,{style:{fontSize:"32px",color:"#fff"}})}),(0,e.jsx)("div",{style:{fontSize:"16px",color:"#52c41a",fontWeight:500},children:"\u767B\u5F55\u6210\u529F"})]}):t.status==="Scanned"?(0,e.jsx)(e.Fragment,{children:(0,e.jsx)("div",{style:{fontSize:"14px",color:"#666"},children:"\u8BF7\u5728\u624B\u673A\u4E0A\u70B9\u51FB\u786E\u8BA4\u6309\u94AE"})}):t.status==="Confirmed"?(0,e.jsxs)(e.Fragment,{children:[(0,e.jsx)(N.Z,{size:"large",tip:"\u767B\u5F55\u4E2D..."}),(0,e.jsx)("div",{style:{fontSize:"14px",color:"#666",marginTop:"8px"},children:"\u6B63\u5728\u767B\u5F55\u7CFB\u7EDF"})]}):(0,e.jsxs)(e.Fragment,{children:[(0,e.jsx)("div",{style:{fontSize:"16px",color:"#333",fontWeight:500,marginBottom:"4px"},children:t.status==="Expired"?"\u4E8C\u7EF4\u7801\u5DF2\u8FC7\u671F":"\u4E8C\u7EF4\u7801\u5DF2\u53D6\u6D88"}),(0,e.jsxs)("a",{onClick:f,style:{display:"flex",alignItems:"center",color:"#1890ff",cursor:"pointer",fontSize:"14px",padding:"8px 16px",border:"1px solid #1890ff",borderRadius:"4px",transition:"all 0.3s"},onMouseEnter:function(i){return i.currentTarget.style.backgroundColor="#f0f5ff"},onMouseLeave:function(i){return i.currentTarget.style.backgroundColor="transparent"},children:[(0,e.jsx)(W.Z,{style:{marginRight:"6px"}}),"\u70B9\u51FB\u5237\u65B0"]})]})})]}):(0,e.jsx)(N.Z,{tip:"\u52A0\u8F7D\u4E2D..."})}),(0,e.jsx)("div",{style:{marginTop:16,color:"#666"},children:t.status==="WaitingScan"?"\u8BF7\u4F7F\u7528\u5FAE\u4FE1\u626B\u7801\u767B\u5F55":""})]})},Ue=Ne,Ve=function(a){var d=a.qrCodeData,t=a.refreshQrCode;return(0,e.jsx)(Ue,{qrCodeData:d||{},refreshQrCode:t||function(){}})},ae=function(a){return[{key:"account",label:"\u8D26\u53F7\u767B\u5F55",children:(0,e.jsx)(Je,v()({},a))},{key:"scan",label:"\u626B\u7801\u767B\u5F55",children:(0,e.jsx)(Ve,v()({},a))}]},Ge=function(){var a=_.ZP.useMessage(),d=y()(a,2),t=d[0],f=d[1],n=x.useRef(),i=(0,x.useState)("account"),E=y()(i,2),B=E[0],M=E[1],k=(0,P.useModel)("@@initialState"),$=k.initialState,U=k.setInitialState,V=x.useState(""),G=y()(V,2),T=G[0],S=G[1],A=x.useState(""),b=y()(A,2),D=b[0],l=b[1],re=x.useState([]),R=y()(re,2),Xe=R[0],ne=R[1],Ye=x.useState(!1),se=y()(Ye,2),ue=se[0],K=se[1],_e=x.useState(!1),oe=y()(_e,2),et=oe[0],ie=oe[1],tt=(0,x.useState)([]),le=y()(tt,2),at=le[0],de=le[1],rt=(0,x.useState)(function(){var u=localStorage.getItem("lastLoginType");return u==="account"||u==="scan"?u:"account"}),ce=y()(rt,2),Q=ce[0],nt=ce[1],ge=(0,P.useIntl)(),st=(0,x.useState)({loading:!1,status:"WaitingScan"}),he=y()(st,2),I=he[0],w=he[1],ut=(0,x.useState)(!1),pe=y()(ut,2),O=pe[0],ot=pe[1];(0,x.useEffect)(function(){ot(window.location.host.indexOf("hnjx.51panda.com")>-1)},[]);var it=function(){var u=F()(c()().mark(function s(g){var m,p,o;return c()().wrap(function(C){for(;;)switch(C.prev=C.next){case 0:return C.prev=0,C.next=3,(0,H.ZP)("/Auth/QrCodeStatus/checkStatus",{errorMessage:!1,method:"GET",data:{qrCodeId:g}});case 3:if(m=C.sent,!m.success){C.next=11;break}if(p={0:"WaitingScan",1:"Scanned",2:"Confirmed",3:"Expired",4:"Canceled"},o=p[m.data.Status],w(function(z){return v()(v()({},z),{},{status:o})}),m.data.Status!==2){C.next=11;break}return F()(c()().mark(function z(){var X,ve;return c()().wrap(function(j){for(;;)switch(j.prev=j.next){case 0:return j.prev=0,j.next=3,(0,H.ZP)("/Auth/QrCodeLogin/login",{method:"POST",data:{qrCodeId:g}});case 3:if(X=j.sent,!X.success){j.next=12;break}return w(function(J){return v()(v()({},J),{},{status:"Success"})}),j.next=8,fe();case 8:ve=new URL(window.location.href).searchParams,setTimeout(function(){P.history.push(ve.get("redirect")||"/")},1e3),j.next=13;break;case 12:w(function(J){return v()(v()({},J),{},{status:"Failed",errorMessage:X.message||"\u767B\u5F55\u5931\u8D25\uFF0C\u8BF7\u91CD\u65B0\u626B\u7801"})});case 13:j.next=18;break;case 15:j.prev=15,j.t0=j.catch(0),w(function(J){return v()(v()({},J),{},{status:"Failed",errorMessage:"\u767B\u5F55\u5931\u8D25\uFF0C\u8BF7\u91CD\u65B0\u626B\u7801"})});case 18:case"end":return j.stop()}},z,null,[[0,15]])}))(),C.abrupt("return",!0);case 11:return C.abrupt("return",!1);case 14:return C.prev=14,C.t0=C.catch(0),console.error("\u68C0\u67E5\u72B6\u6001\u5931\u8D25:",C.t0),w(function(z){return v()(v()({},z),{},{status:"Failed",errorMessage:"\u767B\u5F55\u5931\u8D25\uFF0C\u8BF7\u91CD\u65B0\u626B\u7801"})}),C.abrupt("return",!0);case 19:case"end":return C.stop()}},s,null,[[0,14]])}));return function(g){return u.apply(this,arguments)}}();(0,x.useEffect)(function(){Q==="scan"&&(w(function(u){return v()(v()({},u),{},{loading:!0})}),(0,H.ZP)("/Auth/QrCode/getQrCode",{method:"POST",errorMessage:!1}).then(function(u){u.success?w({qrCodeId:u.data.QrCodeId,qrCodeImage:u.data.QrCodeImage,loading:!1,status:"WaitingScan"}):w({loading:!1,status:"Error"})}).catch(function(){w({loading:!1,status:"Error"})}))},[Q]),(0,x.useEffect)(function(){var u,s=!1;return Q==="scan"&&I.qrCodeId&&I.status!=="Expired"&&I.status!=="Canceled"&&I.status!=="Success"&&I.status!=="Failed"&&I.status!=="Confirmed"&&!s&&(s=!0,u=setInterval(F()(c()().mark(function g(){var m;return c()().wrap(function(o){for(;;)switch(o.prev=o.next){case 0:return o.prev=0,o.next=3,it(I.qrCodeId);case 3:m=o.sent,m&&(s=!1,clearInterval(u)),o.next=11;break;case 7:o.prev=7,o.t0=o.catch(0),s=!1,clearInterval(u);case 11:case"end":return o.stop()}},g,null,[[0,7]])})),1e3)),function(){s=!1,u&&clearInterval(u)}},[Q,I.qrCodeId,I.status]);var fe=function(){var u=F()(c()().mark(function s(){var g,m;return c()().wrap(function(o){for(;;)switch(o.prev=o.next){case 0:return o.next=2,$==null||(g=$.fetchUserInfo)===null||g===void 0?void 0:g.call($);case 2:m=o.sent,m&&(0,Le.flushSync)(function(){U(function(h){return v()(v()({},h),{},{currentUser:m})})});case 4:case"end":return o.stop()}},s)}));return function(){return u.apply(this,arguments)}}(),lt=function(){var u=F()(c()().mark(function s(g){var m,p;return c()().wrap(function(h){for(;;)switch(h.prev=h.next){case 0:return h.prev=0,Ie.w3.data.clear(),h.next=4,(0,Se.x4)(v()(v()({},g),{},{type:B}));case 4:if(m=h.sent,!m.success){h.next=21;break}if(!(m.data&&m.data.accountList&&m.data.accountList.length>0)){h.next=13;break}S("error"),l("\u7ED1\u5B9A\u4E86\u591A\u6761\u8D26\u6237\uFF0C\u8BF7\u9009\u62E9\u9700\u8981\u767B\u5F55\u7684\u8D26\u6237"),ne(m.data.accountList),n==null||(p=n.current)===null||p===void 0||p.setFieldValue("id",void 0),h.next=19;break;case 13:return h.next=15,fe();case 15:if(P.history){h.next=17;break}return h.abrupt("return");case 17:return P.history.push("/"),h.abrupt("return");case 19:h.next=23;break;case 21:S("error"),l(m.message);case 23:h.next=27;break;case 25:h.prev=25,h.t0=h.catch(0);case 27:case"end":return h.stop()}},s,null,[[0,25]])}));return function(g){return u.apply(this,arguments)}}(),dt=function(s){var g=s;nt(g),localStorage.setItem("lastLoginType",g)},me={status:T,statusMessage:D,accountList:Xe,loginRef:n,showHnJxChangePwd:ue,setShowHnJxChangePwd:K,onAccountListClear:function(){ne([]),S(""),l("")},qrCodeData:I,refreshQrCode:function(){w(function(s){return v()(v()({},s),{},{loading:!0,status:"WaitingScan",errorMessage:void 0})}),(0,H.ZP)("/Auth/QrCode/getQrCode",{method:"POST",errorMessage:!1}).then(function(s){s.success?w({qrCodeId:s.data.QrCodeId,qrCodeImage:s.data.QrCodeImage,loading:!1,status:"WaitingScan"}):w({loading:!1,status:"Error"})}).catch(function(){w({loading:!1,status:"Error"})})},isHnjxHost:O};return(0,e.jsxs)(e.Fragment,{children:[f,(0,e.jsxs)(je.ZP,{theme:{token:{borderRadius:0}},children:[(0,e.jsx)(P.Helmet,{children:(0,e.jsxs)("title",{children:[ge.formatMessage({id:"menu.login",defaultMessage:"\u767B\u5F55\u9875"})," - \u76FC\u8FBE\u8F6F\u4EF6"]})}),(0,e.jsx)("div",{style:{backgroundColor:"white",minHeight:"max(100vh, 660px)",height:"max(100vh, 660px)"},className:"login-form",children:(0,e.jsx)(we.LoginFormPage,{className:"login-form-page",formRef:n,submitter:{resetButtonProps:!1,render:function(s,g){return Q==="scan"?null:g}},onFinish:function(){var u=F()(c()().mark(function s(g){return c()().wrap(function(p){for(;;)switch(p.prev=p.next){case 0:return console.log(g),p.next=3,lt(g);case 3:case"end":return p.stop()}},s)}));return function(s){return u.apply(this,arguments)}}(),backgroundImageUrl:"https://cdn.51panda.com/c3f153dd-80a0-40a8-9a54-a3b7d6717ab8.jpg",logo:(0,e.jsx)("img",{alt:"logo",src:O?"/hnjx.png":"/logo.svg"}),title:O?"\u6E56\u5357\u9A7E\u534F":"\u76FC\u8FBE\u8F6F\u4EF6",subTitle:O?"\u6E56\u5357\u7701\u673A\u52A8\u8F66\u9A7E\u9A76\u57F9\u8BAD\u6559\u7EC3\u5458\u7BA1\u7406\u7CFB\u7EDF":ge.formatMessage({id:"pages.layouts.userLayout.title"}),containerStyle:{padding:"80px 50px 30px 50px",borderRadius:8},activityConfig:{style:{boxShadow:"0px 0px 8px rgba(0, 0, 0, 0.2)",color:"rgba(255,255,255,0.65)",borderRadius:4,backgroundColor:"rgba(0,0,0,0.25)",backdropFilter:"blur(4px)",display:""},title:"\u5DE5\u4FE1\u90E8\u5907\u6848\u53F7",subTitle:(0,e.jsx)("a",{href:"https://beian.miit.gov.cn/",target:"_blank",style:{color:"rgba(255,255,255,0.65)"},children:"\u6E58ICP\u59072022002170\u53F7"})},style:{color:"rgba(0, 0, 0, 0.80)"},actions:(0,e.jsxs)("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",marginTop:"120px",flexDirection:"row",flexWrap:"nowrap"},children:[(0,e.jsx)("div",{style:{display:"flex"},children:(0,e.jsx)(Fe.Z,{src:"https://beian.mps.gov.cn/web/assets/logo01.6189a29f.png",style:{width:"20px"}})}),(0,e.jsx)("div",{style:{display:"flex",marginLeft:"10px",fontSize:"12px"},children:(0,e.jsx)("a",{href:"https://beian.mps.gov.cn/#/query/webSearch?code=**************",rel:"noreferrer",target:"_blank",children:"\u6E58\u516C\u7F51\u5B89\u5907**************"})})]}),children:O?ae(me)[0].children:(0,e.jsx)(Pe.Z,{centered:!0,activeKey:Q,onChange:dt,items:ae(me)})})}),(0,e.jsx)(ee,{visible:et,onVisibleChange:ie,onFinish:function(){var u=F()(c()().mark(function s(g){return c()().wrap(function(p){for(;;)switch(p.prev=p.next){case 0:return p.next=2,(0,H.ZP)("/Auth/sendPassWord",{method:"POST",data:g}).then(function(o){if(o&&o.success)if(t.success(o.message),o.data.users&&o.data.users.length>0){var h=[];o.data.users.map(function(C){h.push({text:C.Account,lable:C.Account,value:C.Account})}),de(h)}else de([]),ie(!1)});case 2:case"end":return p.stop()}},s)}));return function(s){return u.apply(this,arguments)}}(),changePwdAccountList:at}),(0,e.jsx)(ee,{visible:ue,onVisibleChange:K,onFinish:function(){var u=F()(c()().mark(function s(g){return c()().wrap(function(p){for(;;)switch(p.prev=p.next){case 0:return p.next=2,(0,H.ZP)("/HNJiaXie/Hnjx/SetPassWord",{method:"POST",data:g}).then(function(o){o&&o.success&&(t.success(o.message),K(!1))});case 2:case"end":return p.stop()}},s)}));return function(s){return u.apply(this,arguments)}}(),tenantId:"1F79F34F-7EA8-4203-8202-7C8C6E742115"})]})]})},Ke=Ge}}]);
