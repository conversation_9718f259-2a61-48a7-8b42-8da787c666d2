"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[8163],{50045:function(se,Z,a){a.r(Z),a.d(Z,{default:function(){return ne}});var q=a(15009),k=a.n(q),J=a(99289),O=a.n(J),U=a(5574),s=a.n(U),$=a(51477),Q=a(11774),M=a(45360),E=a(85576),N=a(18922),V=a(88760),l=a(67294),C=a(78158),Y=a(89545),G=a(49187),H=a(42509),S=a(83622),e=a(85893),z=l.forwardRef(function(t,F){var g=M.ZP.useMessage(),c=s()(g,2),i=c[0],x=c[1],f=(0,l.useRef)(),p=(0,l.useRef)();function m(d){return(0,C.WY)("/Pay/Base/PayAccount/getPayMethodList",{method:"POST"})}var y=function(){i.loading({content:"\u6B63\u5728\u7ED1\u5B9A\u5FAE\u4FE1",key:"loading",duration:0}),(0,C.WY)("/Pay/JlPay/authBind",{method:"PUT",data:{Id:t.Id}}).then(function(o){i.destroy("loading"),o.success?i.success("\u7ED1\u5B9A\u6210\u529F"):i.error(o.message||"\u7ED1\u5B9A\u5931\u8D25")})},v=function(){i.loading({content:"\u6B63\u5728\u52A0\u673A\u63D0\u4EA4",key:"loading",duration:0}),(0,C.WY)("/Pay/JlPay/addDevice",{method:"PUT",data:{Id:t.Id}}).then(function(o){i.destroy("loading"),o.success?(i.success("\u52A0\u673A\u6210\u529F"),f.current.get(t.Id)):i.error(o.message||"\u52A0\u673A\u5931\u8D25")})},I=function(){p.current.get(void 0)};return(0,e.jsxs)(e.Fragment,{children:[x,(0,e.jsx)(Y.default,{ref:p,formItems:[{name:"Id",type:"hidden",label:"",placeholder:"",required:!0,value:t.Id},{name:"AppId",type:"input",label:"\u5FAE\u4FE1AppId",placeholder:"\u8BF7\u8F93\u5165\u5FAE\u4FE1AppId",required:!0}],modifyTitle:"\u4ED8\u5457\u7ED1\u5B9A\u5FAE\u4FE1",insertTitle:"\u4ED8\u5457\u7ED1\u5B9A\u5FAE\u4FE1",initData:{Id:t.Id},getPath:"",setPath:"/Pay/FbPay/configWxJsapiPath/{id}",width:480}),(0,e.jsx)(Y.default,{ref:f,formItems:[{name:"Id",type:"hidden",label:"",placeholder:"",required:!0},{type:"group",children:[{name:"Name",type:"input",label:"\u8D26\u6237\u6635\u79F0",placeholder:"\u8BF7\u8F93\u5165\u8D26\u6237\u6635\u79F0",width:580,required:!0},{name:"SortCode",type:"number",label:"\u663E\u793A\u6392\u5E8F",placeholder:"\u8BF7\u8F93\u5165\u663E\u793A\u6392\u5E8F",width:180,required:!0}]},{type:"divider",label:"\u76F8\u5173\u4FE1\u606F"},{type:"group",children:[{name:"PayMethod",type:"select",request:m,label:"\u652F\u4ED8\u65B9\u5F0F",placeholder:"\u8BF7\u8F93\u5165\u652F\u4ED8\u65B9\u5F0F",width:380,required:!0,allowClear:!1},{name:"CompanyName",type:"input",label:"\u516C\u53F8\u540D\u79F0",placeholder:"\u8BF7\u8F93\u5165\u8425\u4E1A\u6267\u7167\u7684\u5168\u79F0",width:380,required:!1}]},{type:"group",children:[{name:"Account",type:"input",label:"\u652F\u4ED8\u8D26\u53F7",placeholder:"\u8BF7\u8F93\u5165\u652F\u4ED8\u8D26\u53F7",width:380,required:!1},{name:"PassWord",type:"input",label:"\u8D26\u6237\u5BC6\u7801",placeholder:"\u8BF7\u8F93\u5165\u8D26\u6237\u5BC6\u7801",width:380,required:!1}]},{type:"group",children:[{name:"AppId",type:"input",label:"\u5E94\u7528\u7F16\u53F7",placeholder:"\u8BF7\u8F93\u5165 AppId",width:380,required:!1},{name:"AppSecret",type:"input",label:"\u5E94\u7528\u79D8\u94A5",placeholder:"\u8BF7\u8F93\u5165 AppSecret",width:380,required:!1}]},{type:"group",children:[{name:"StoreId",type:"input",label:"\u5E97\u94FA\u7F16\u7801",placeholder:"\u8BF7\u8F93\u5165\u5E97\u94FA\u7F16\u7801",width:380,required:!1},{name:"CashierId",type:"input",label:"\u5458\u5DE5\u7F16\u7801",placeholder:"\u8BF7\u8F93\u5165\u5458\u5DE5\u7F16\u7801",width:380,required:!1}]},{type:"group",children:[{name:"Location",type:"input",label:"\u7ECF\u7EAC\u5EA6\u503C",placeholder:"\u8BF7\u8F93\u5165\u7ECF\u7EAC\u5EA6\u503C",width:380,required:!1},{name:"Address",type:"input",label:"\u516C\u53F8\u5730\u5740",placeholder:"\u8BF7\u8F93\u5165\u516C\u53F8\u5730\u5740",width:380,required:!1}]},{type:"group",children:[{name:"MerchantId",type:"input",label:"\u5546\u6237\u7F16\u53F7",placeholder:"\u8BF7\u8F93\u5165\u5546\u6237\u7F16\u53F7",width:380,required:!1},{name:"Uid",type:"input",label:"\u4E09\u65B9\u7CFB\u7EDF",placeholder:"\u8BF7\u8F93\u5165\u4E09\u65B9\u7CFB\u7EDF\u7F16\u7801",width:380,required:!1}]},{type:"group",children:[{name:"WxAppId",type:"input",label:"\u7ED1\u5B9A\u5FAE\u4FE1",placeholder:"\u4F7F\u7528\u7684\u5FAE\u4FE1AppId",width:380,required:!1},{name:"SubAppId",type:"input",label:"\u5FAE\u4FE1\u7A0B\u5E8F",placeholder:"\u5FAE\u4FE1\u914D\u7F6E\u4EE5\u540E\u751F\u6210\u7684",width:380,required:!1,disabled:!0}]},{type:"group",children:[{name:"BankName",type:"input",label:"\u94F6\u884C\u540D\u79F0",placeholder:"\u7ED3\u7B97\u94F6\u884C\u540D\u79F0",width:380,required:!1},{name:"BankCode",type:"input",label:"\u94F6\u884C\u4EE3\u7801",placeholder:"\u7ED3\u7B97\u94F6\u884C\u4EE3\u7801",width:380,required:!1}]},{type:"group",children:[{name:"BankCardNumber",type:"input",label:"\u7ED3\u7B97\u8D26\u53F7",placeholder:"\u7ED3\u7B97\u94F6\u884C\u8D26\u53F7",width:380,required:!1},{name:"DeviceId",type:"input",label:"\u8BBE\u5907\u7F16\u53F7",placeholder:"\u901A\u8FC7\u52A0\u673A\u6765\u83B7\u53D6\u8BBE\u5907\u7F16\u53F7",width:380,required:!1,disabled:!1}]},{type:"group",children:[{name:"ComputerAccountId",type:"select",request:G.dG,label:"\u7ED3\u7B97\u8D26\u6237",placeholder:"\u9009\u62E9\u7ED3\u7B97\u8D26\u6237",width:380,required:!1}]},{type:"divider",label:"\u670D\u52A1\u5546\u7684\u76F8\u5173\u4FE1\u606F"},{type:"group",children:[{name:"VendorSn",type:"input",label:"\u7CFB\u7EDF\u7F16\u53F7",placeholder:"\u8BF7\u8F93\u5165\u670D\u52A1\u5546\u7F16\u53F7",width:380,required:!1},{name:"VendorAppSecret",type:"input",label:"\u7CFB\u7EDF\u79D8\u94A5",placeholder:"\u8BF7\u8F93\u5165\u670D\u52A1\u5546\u7CFB\u7EDF\u79D8\u94A5",width:380,required:!1}]}],modifyTitle:"\u8D26\u6237\u4FE1\u606F\u7F16\u8F91",insertTitle:"\u6DFB\u52A0\u8D26\u6237\u4FE1\u606F",initData:{Id:void 0,SortCode:9999},getPath:"/Pay/Base/PayAccount/getAccountInfo",setPath:"/Pay/Base/PayAccount/setAccountInfo",width:960,onCallBack:function(){t.onCallBack&&t.onCallBack()},footer:[t.Id!=null&&(0,e.jsxs)(e.Fragment,{children:[(0,e.jsx)(S.ZP,{onClick:y,children:"\u5609\u8054\u7ED1\u5B9A\u5FAE\u4FE1"}),(0,e.jsx)(S.ZP,{onClick:v,children:"\u5609\u8054\u52A0\u673A"}),(0,e.jsx)(S.ZP,{onClick:I,children:"\u4ED8\u5457\u7ED1\u5B9A\u5FAE\u4FE1"})]})]}),t.Id==null&&(0,e.jsx)(S.ZP,{icon:(0,e.jsx)(H.Z,{}),style:t.style,type:"primary",ghost:!0,onClick:function(){return f.current.get(void 0)},children:"\u6DFB\u52A0"},"place-info-add"),t.Id!=null&&(0,e.jsx)("a",{onClick:function(){f.current.get(t.Id)},title:"\u7F16\u8F91",style:t.style,children:t.Name==""?"\u65E0":t.Name},"edit")]})}),L=z,K=a(1291),X=a(48054),_=a(56595),ee=l.forwardRef(function(t,F){var g=M.ZP.useMessage(),c=s()(g,2),i=c[0],x=c[1],f=(0,l.useState)(!1),p=s()(f,2),m=p[0],y=p[1],v=(0,l.useState)(!1),I=s()(v,2),d=I[0],o=I[1],w=(0,l.useState)(""),A=s()(w,2),T=A[0],D=A[1],j=function(){D(""),o(!0),y(!0),(0,C.WY)("/Pay/Base/PayAccount/getPayQCode",{method:"POST",data:{Id:t.Id}}).then(function(b){b.success?(D(b.data),o(!1)):y(!1)})};return(0,e.jsxs)(e.Fragment,{children:[x,(0,e.jsx)(E.Z,{open:m,onCancel:function(){y(!1)},title:"\u7EBF\u4E0A\u4ED8\u6B3E",footer:!1,loading:d,children:(0,e.jsxs)("div",{style:{paddingTop:"20px",textAlign:"center"},children:[d&&(0,e.jsx)(X.Z.Image,{active:d,style:{width:300,height:300}}),!d&&(0,e.jsx)(_.Z,{width:300,src:"data:image/png;base64,"+T,preview:!1},"barCodeImage")]})}),(0,e.jsx)("a",{onClick:j,children:(0,e.jsx)(K.Z,{})})]})}),ae=ee,te=function(){var F=M.ZP.useMessage(),g=s()(F,2),c=g[0],i=g[1],x=(0,l.useState)(!1),f=s()(x,2),p=f[0],m=f[1],y=(0,l.useState)(null),v=s()(y,2),I=v[0],d=v[1],o=(0,l.useState)(null),w=s()(o,2),A=w[0],T=w[1],D=(0,l.useState)(!1),j=s()(D,2),R=j[0],b=j[1],W=(0,l.useRef)(),oe=(0,l.useRef)(),le=function(r){return(0,e.jsx)(L,{Id:r.Id,Name:r.Name})},de=function(r){return(0,e.jsx)(ae,{Id:r.Id})},ce=function(r){d(r),m(!0)},re=function(){var h=O()(k()().mark(function r(){var u,B,P;return k()().wrap(function(n){for(;;)switch(n.prev=n.next){case 0:if(A){n.next=3;break}return c.error("\u8BF7\u9009\u62E9\u8D26\u5355\u65E5\u671F"),n.abrupt("return");case 3:return n.prev=3,b(!0),n.next=7,(0,C.ZP)("/Pay/JlPay/downloadBill",{method:"post",data:{Id:I.Id,BillType:"1",BillDate:A.format("YYYYMMDD")}});case 7:u=n.sent,u&&u.success&&(typeof u.data=="string"&&u.data.startsWith("http")?window.open(u.data,"_blank"):(B=window.URL.createObjectURL(new Blob([u.data])),P=document.createElement("a"),P.href=B,P.setAttribute("download","\u8D26\u5355_".concat(A.format("YYYYMMDD"),".xlsx")),document.body.appendChild(P),P.click(),document.body.removeChild(P),window.URL.revokeObjectURL(B)),c.success("\u4E0B\u8F7D\u6210\u529F"),m(!1)),n.next=14;break;case 11:n.prev=11,n.t0=n.catch(3),c.error("\u4E0B\u8F7D\u5931\u8D25");case 14:return n.prev=14,b(!1),n.finish(14);case 17:case"end":return n.stop()}},r,null,[[3,11,14,17]])}));return function(){return h.apply(this,arguments)}}(),ie=[{dataIndex:"RowIndex",title:"\u5E8F\u53F7",align:"center"},{dataIndex:"Name",align:"center",title:"\u540D\u79F0",render:function(r,u,B){return le(u)}},{dataIndex:"Name",align:"center",title:"\u6536\u6B3E",render:function(r,u,B){return de(u)}},{dataIndex:"PayMethodName",align:"center",title:"\u652F\u4ED8\u901A\u9053"},{dataIndex:"Account",title:"\u652F\u4ED8\u8D26\u53F7",align:"center"},{dataIndex:"CreateTime",title:"\u521B\u5EFA\u65F6\u95F4",align:"center"},{dataIndex:"AppId",title:"\u5E94\u7528\u7F16\u53F7",align:"center"},{dataIndex:"BankCardNumber",title:"\u7ED3\u7B97\u8D26\u53F7",align:"center"}],ue=[(0,e.jsx)(L,{Id:void 0,style:{marginLeft:"10px"},onCallBack:function(){W.current.reload()}})];return(0,e.jsxs)(Q._z,{header:{breadcrumb:{},title:""},children:[i,(0,e.jsx)($.Z,{ref:W,formCols:[],columns:ie,tableButtons:ue,getPath:"/Pay/Base/PayAccount/getAccountList",rowKey:"Id"},"jx-account-list-table"),(0,e.jsx)(E.Z,{title:"\u4E0B\u8F7D\u8D26\u5355",open:p,onOk:re,onCancel:function(){return m(!1)},okText:"\u4E0B\u8F7D",cancelText:"\u53D6\u6D88",confirmLoading:R,children:(0,e.jsx)(N.Z,{layout:"vertical",children:(0,e.jsx)(N.Z.Item,{label:"\u9009\u62E9\u8D26\u5355\u65E5\u671F",required:!0,children:(0,e.jsx)(V.default,{onChange:function(r){return T(r)},style:{width:"100%"}})})})})]})},ne=te}}]);
