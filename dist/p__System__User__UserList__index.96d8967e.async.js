"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[942],{61569:function(Ae,G){var n={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M832 464h-68V240c0-70.7-57.3-128-128-128H388c-70.7 0-128 57.3-128 128v224h-68c-17.7 0-32 14.3-32 32v384c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V496c0-17.7-14.3-32-32-32zM332 240c0-30.9 25.1-56 56-56h248c30.9 0 56 25.1 56 56v224H332V240zm460 600H232V536h560v304zM484 701v53c0 4.4 3.6 8 8 8h40c4.4 0 8-3.6 8-8v-53a48.01 48.01 0 10-56 0z"}}]},name:"lock",theme:"outlined"};G.Z=n},85170:function(Ae,G){var n={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M400 317.7h73.9V656c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V317.7H624c6.7 0 10.4-7.7 6.3-12.9L518.3 163a8 8 0 00-12.6 0l-112 141.7c-4.1 5.3-.4 13 6.3 13zM878 626h-60c-4.4 0-8 3.6-8 8v154H214V634c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v198c0 17.7 14.3 32 32 32h684c17.7 0 32-14.3 32-32V634c0-4.4-3.6-8-8-8z"}}]},name:"upload",theme:"outlined"};G.Z=n},57308:function(Ae,G,n){var me=n(67294),o=n(61569),Se=n(57079);function A(){return A=Object.assign?Object.assign.bind():function(le){for(var re=1;re<arguments.length;re++){var ue=arguments[re];for(var q in ue)Object.prototype.hasOwnProperty.call(ue,q)&&(le[q]=ue[q])}return le},A.apply(this,arguments)}const De=(le,re)=>me.createElement(Se.Z,A({},le,{ref:re,icon:o.Z})),v=me.forwardRef(De);G.Z=v},7625:function(Ae,G,n){n.r(G),n.d(G,{default:function(){return wa}});var me=n(15009),o=n.n(me),Se=n(99289),A=n.n(Se),De=n(5574),v=n.n(De),le=n(51477),re=n(11774),ue=n(45360),q=n(85576),de=n(83622),l=n(67294),ze=n(98820),Oe=n(89545),Ye=n(57308),e=n(85893),Qe=l.forwardRef(function(a,S){var D=ue.ZP.useMessage(),C=v()(D,2),I=C[0],B=C[1],$=(0,l.useRef)();return(0,e.jsxs)(e.Fragment,{children:[B,(0,e.jsx)(Oe.default,{ref:$,formItems:[{name:"Id",type:"hidden",label:"",placeholder:"",required:!1,value:a.userId},{name:"NewPWD",type:"password",label:"\u65B0\u7684\u5BC6\u7801",placeholder:"\u8BF7\u8F93\u5165\u5BC6\u7801",required:!0},{name:"NewPWD2",type:"password",label:"\u786E\u8BA4\u5BC6\u7801",placeholder:"\u8BF7\u8F93\u5165\u518D\u6B21\u5BC6\u7801",required:!0}],modifyTitle:"\u5BC6\u7801\u4FEE\u6539",insertTitle:"\u6DFB\u52A0\u8D44\u6599\u72B6\u6001",initData:{Id:a.userId},getPath:"",setPath:"/Auth/UserLogOn/changePassWord",width:400,onCallBack:function(){a.onCallBack&&a.onCallBack()}}),(0,e.jsx)("a",{onClick:function(){$.current.open()},children:(0,e.jsx)(Ye.Z,{})})]})}),Xe=Qe,Ge=n(6452),Te=n(66557),k=n(59637),qe=l.forwardRef(function(a,S){var D=function(I){I?Te.Z.post({message:"\u662F\u5426\u786E\u8BA4\u6253\u5F00 ".concat(a.realName," \u7684\u8D26\u53F7?"),setPath:"/SystemManage/User/openUser",data:{Id:a.userId},method:"PUT",messageApi:a.messageApi,onCallBack:function(){a.onCallBack&&a.onCallBack()}}):Te.Z.post({message:"\u662F\u5426\u786E\u8BA4\u5173\u95ED ".concat(a.realName," \u7684\u8D26\u53F7?"),setPath:"/SystemManage/User/closeUser",data:{Id:a.userId},method:"PUT",messageApi:a.messageApi,onCallBack:function(){a.onCallBack&&a.onCallBack()}})};return(0,e.jsx)(e.Fragment,{children:a.isEnabled?(0,e.jsx)(k.Z,{checked:!0,onChange:D}):(0,e.jsx)(k.Z,{checked:!1,onChange:D})})}),_e=qe,ea=n(19632),aa=n.n(ea),na=n(97857),ge=n.n(na),O=n(58930),be=n(2618),_=n(23750),Be=n(88634),N=n(78158),ta=n(85170),ra=n(57079);function we(){return we=Object.assign?Object.assign.bind():function(a){for(var S=1;S<arguments.length;S++){var D=arguments[S];for(var C in D)Object.prototype.hasOwnProperty.call(D,C)&&(a[C]=D[C])}return a},we.apply(this,arguments)}const ua=(a,S)=>l.createElement(ra.Z,we({},a,{ref:S,icon:ta.Z}));var la=l.forwardRef(ua),Ne=n(64121),r=n(18922),W=n(71230),We=n(15746),ce=n(42075),se=n(25278),ve=n(37720),sa=n(56595),$e=n(96074),ia=n(59720),oa=n(60024),da=n(80688),ca=n(27484),Ce=n.n(ca),fa=n(13769),ha=n.n(fa),ma=n(52677),ga=n.n(ma),va=n(68639),Ca=n(51042),xa=["key","name"],pa=l.forwardRef(function(a,S){var D=ue.ZP.useMessage(),C=v()(D,2),I=C[0],B=C[1],$=r.Z.useForm(),H=v()($,1),ee=H[0];(0,l.useEffect)(function(){ie()},[]),(0,l.useImperativeHandle)(S,function(){return{submit:function(){ee.submit()}}});var fe=function(w){var b=ae(w);(0,N.ZP)("/JiaXiao/JxClass/setClassCarTypePriceInfo",{method:"PUT",data:{UserId:a.userId,data:b.data}}).then(function(J){J.success&&I.success(J.message),a.onSubmitCallback&&a.onSubmitCallback()})},ie=function(){(0,N.ZP)("/JiaXiao/JxClass/getClassCarTypeByUserList",{method:"POST",data:{UserId:a.userId,size:9999}}).then(function(w){w.success&&ee.setFieldsValue({data:w.data.data}),a.onSubmitCallback&&a.onSubmitCallback()})};function ae(j){if(Array.isArray(j))return j.map(function(J){return ae(J)});var w={};for(var b in j)j[b]===null?w[b]=void 0:ga()(j[b])==="object"?w[b]=ae(j[b]):w[b]=j[b];return w}return(0,e.jsxs)("div",{className:"form-list",children:[B,(0,e.jsx)(r.Z,{form:ee,name:"form",onFinish:fe,layout:"vertical",onFinishFailed:function(){a.onSubmitCallback&&a.onSubmitCallback()},children:(0,e.jsx)(r.Z.List,{name:"data",children:function(w,b){var J=b.add,oe=b.remove;return(0,e.jsxs)(e.Fragment,{children:[w.map(function(z){var K=z.key,T=z.name,V=ha()(z,xa);return(0,e.jsxs)(ce.Z,{style:{display:"flex",marginBottom:0,width:"100%"},align:"baseline",children:[(0,e.jsx)(r.Z.Item,{name:[T,"Id"],hidden:!0}),(0,e.jsx)(r.Z.Item,ge()(ge()({name:[T,"CarType"],label:K==0&&"\u8F66\u578B"},V),{},{rules:[{validator:function(){var U=A()(o()().mark(function i(M,P){return o()().wrap(function(d){for(;;)switch(d.prev=d.next){case 0:if(!(P==null||P=="")){d.next=3;break}throw I.error("\u8BF7\u9009\u62E9\u8F66\u578B"),new Error;case 3:case"end":return d.stop()}},i)}));function R(i,M){return U.apply(this,arguments)}return R}()}],children:(0,e.jsx)(O.Z,{title:"",width:200,listHeight:600,allowClear:!1,placeholder:"\u8F66\u578B",request:function(){return(0,_.kA)({SearchKey:"",ShowDetail:!1})},multiple:!1,onChange:function(R){}},"CarTypes-MySelect")})),(0,e.jsx)(r.Z.Item,{name:[T,"JxClassId"],label:K!=0?"":"\u62A5\u540D\u73ED\u578B",rules:[{validator:function(){var U=A()(o()().mark(function i(M,P){return o()().wrap(function(d){for(;;)switch(d.prev=d.next){case 0:if(!(P==null||P=="")){d.next=3;break}throw I.error("\u8BF7\u9009\u62E9\u73ED\u578B"),new Error;case 3:case"end":return d.stop()}},i)}));function R(i,M){return U.apply(this,arguments)}return R}()}],children:(0,e.jsx)(O.Z,{title:"",width:200,listHeight:600,allowClear:!1,placeholder:"\u62A5\u540D\u73ED\u578B",request:_.cL,multiple:!1,onChange:function(R){}},"JxClassId-MySelect")}),(0,e.jsx)(r.Z.Item,{name:[T,"PayMoney"],label:K!=0?"":"\u62A5\u540D\u4EF7\u94B1",rules:[{validator:function(){var U=A()(o()().mark(function i(M,P){return o()().wrap(function(d){for(;;)switch(d.prev=d.next){case 0:if(P!=""){d.next=3;break}throw I.error("\u8BF7\u9009\u62E9\u62A5\u540D\u4EF7\u683C"),new Error;case 3:case"end":return d.stop()}},i)}));function R(i,M){return U.apply(this,arguments)}return R}()}],children:(0,e.jsx)(se.Z,{placeholder:"\u62A5\u540D\u4EF7\u94B1",style:{width:140}})}),(0,e.jsx)(r.Z.Item,{name:[T,"FirstPayMoney"],label:K!=0?"":"\u9996\u4ED8",rules:[{validator:function(){var U=A()(o()().mark(function i(M,P){return o()().wrap(function(d){for(;;)switch(d.prev=d.next){case 0:if(P!=""){d.next=3;break}throw I.error("\u8BF7\u8F93\u5165\u9996\u4ED8\u91D1\u989D"),new Error;case 3:case"end":return d.stop()}},i)}));function R(i,M){return U.apply(this,arguments)}return R}()}],children:(0,e.jsx)(se.Z,{placeholder:"\u9996\u4ED8",style:{width:140}})}),(0,e.jsx)(r.Z.Item,{name:[T,"Id"],label:K!=0?"":" ",children:(0,e.jsx)(va.Z,{onClick:function(){return oe(T)}})})]},K)}),(0,e.jsx)(r.Z.Item,{style:{marginTop:10},children:(0,e.jsx)(de.ZP,{type:"dashed",onClick:function(){return J()},block:!0,icon:(0,e.jsx)(Ca.Z,{}),children:"\u6DFB\u52A0\u914D\u7F6E"})})]})}})})]})}),ya=pa,Ia=l.forwardRef(function(a,S){return(0,l.useEffect)(function(){},[]),(0,e.jsx)(e.Fragment,{})}),Fa=Ia,ja=l.forwardRef(function(a,S){return(0,l.useEffect)(function(){},[]),(0,e.jsx)(e.Fragment,{})}),Za=ja,He=n(4584),Ra=n(96486),Ea=n(50335),Aa=l.forwardRef(function(a,S){var D=(0,He.Z)(),C=v()(D,1),I=C[0],B=(0,He.Z)(),$=v()(B,1),H=$[0],ee=(0,l.useRef)(),fe=(0,l.useState)(!1),ie=v()(fe,2),ae=ie[0],j=ie[1],w=(0,l.useState)(!1),b=v()(w,2),J=b[0],oe=b[1],z=(0,l.useState)(!1),K=v()(z,2),T=K[0],V=K[1],U=(0,l.useState)(void 0),R=v()(U,2),i=R[0],M=R[1],P=(0,l.useState)(void 0),ne=v()(P,2),d=ne[0],xe=ne[1],pe=(0,l.useState)("userinfo-1"),ye=v()(pe,2),Y=ye[0],Ue=ye[1],Ie=(0,l.useState)(1),Re=v()(Ie,2),te=Re[0],Pe=Re[1],Le=(0,l.useState)(),Fe=v()(Le,2),je=Fe[0],Ze=Fe[1];(0,l.useImperativeHandle)(S,function(){return{open:s}});var Me=(0,l.useState)(),Ee=v()(Me,2),he=Ee[0],x=Ee[1],s=function(){var m=A()(o()().mark(function g(){return o()().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:oe(!0),Ze(null),a.messageApi.loading({content:"\u6B63\u5728\u52A0\u8F7D",key:"loading",duration:0}),(0,N.WY)("/Tenant/getMyTenantType",{method:"POST"}).then(function(){var Z=A()(o()().mark(function y(c){var E;return o()().wrap(function(X){for(;;)switch(X.prev=X.next){case 0:if(!c.success){X.next=7;break}return x(c.data),X.next=4,(0,Be.mC)();case 4:c=X.sent,E=c.data,a.userId===void 0?(M({Id:void 0,AllowTime:[Ce()("2000-01-01"),Ce()("2099-01-01")],LockDate:[Ce()("2000-01-01"),Ce()("2000-01-01")],IsEnabled:!0,Sex:1,IsAdmin:!1}),j(!0),a.messageApi.destroy("loading")):(0,N.WY)("/SystemManage/UserInfo/".concat(a.userId),{method:"POST",data:{}}).then(function(u){u.success&&(M(u.data),E.length>0&&E.map(function(Ve){Ve.values=u.data.CategoryIds?u.data.CategoryIds.filter(function(ke){return ke.CategoryId==Ve.Id}).map(function(ke){return ke.Id}):[]}),xe(E),j(!0),a.messageApi.destroy("loading"))});case 7:case"end":return X.stop()}},y)}));return function(y){return Z.apply(this,arguments)}}());case 4:case"end":return t.stop()}},g)}));return function(){return m.apply(this,arguments)}}();(0,l.useEffect)(function(){i&&(I.setFieldsValue(i),F(),oe(!1))},[i]);var L={name:"File",showUploadList:!1,data:{Id:a.userId},beforeUpload:function(g){return g.type!=="image/png"&&g.type!=="image/jpeg"&&g.type!=="image/jpg"?(a.messageApi.error("\u6587\u4EF6\u7C7B\u578B\u4E0D\u652F\u6301,\u7CFB\u7EDF\u53EA\u652F\u6301 png, jpeg, jpg \u7C7B\u578B\u7684\u56FE\u7247"),Ne.Z.LIST_IGNORE):(a.messageApi.loading({content:"\u6B63\u5728\u4E0A\u4F20",key:"loading",duration:0}),!0)},customRequest:function(){var m=A()(o()().mark(function f(t){var Z,y,c,E,h;return o()().wrap(function(u){for(;;)switch(u.prev=u.next){case 0:return Z=t.file,y=t.onSuccess,c=t.onError,u.prev=1,E=new FormData,E.append("File",Z),E.append("Id",a.userId||""),u.next=7,(0,N.WY)("/SystemManage/UserInfo/uploadHeadImg",{method:"formdata",data:E});case 7:h=u.sent,h.success?(a.messageApi.destroy("loading"),a.messageApi.success(h.message),y(h),s()):(a.messageApi.destroy("loading"),a.messageApi.error(h.message),c(h)),u.next=16;break;case 11:u.prev=11,u.t0=u.catch(1),a.messageApi.destroy("loading"),a.messageApi.error("\u4E0A\u4F20\u5931\u8D25"),c(u.t0);case 16:case"end":return u.stop()}},f,null,[[1,11]])}));function g(f){return m.apply(this,arguments)}return g}()},F=function(){var g=(0,e.jsxs)(r.Z,{initialValues:i,form:I,onFinish:function(t){V(!0),t.Id=a.userId,t.Id==null&&(t.Id="00000000-0000-0000-0000-000000000000"),(0,N.WY)("/SystemManage/UserInfo/".concat(t.Id),{method:"PUT",data:t}).then(function(Z){V(!1),Z.success&&(a.messageApi.success(Z.message),a.userId==null&&(I.resetFields(),j(!1),a.onCallBack&&a.onCallBack()))})},children:[(0,e.jsxs)(W.Z,{gutter:[32,8],children:[(0,e.jsxs)(We.Z,{span:19,children:[(0,e.jsxs)(ce.Z,{children:[(0,e.jsx)(r.Z.Item,{label:"\u771F\u5B9E\u59D3\u540D",name:"RealName",rules:[{validator:function(){var f=A()(o()().mark(function Z(y,c){return o()().wrap(function(h){for(;;)switch(h.prev=h.next){case 0:if(c!=null){h.next=2;break}throw new Error("\u8BF7\u8F93\u5165\u7528\u6237\u59D3\u540D");case 2:case"end":return h.stop()}},Z)}));function t(Z,y){return f.apply(this,arguments)}return t}()}],children:(0,e.jsx)(se.Z,{name:"RealName",placeholder:"\u8BF7\u8F93\u5165\u771F\u5B9E\u59D3\u540D",style:{width:"250px"},showCount:!0})}),(0,e.jsx)(r.Z.Item,{style:{paddingLeft:"25px"},label:"\u624B\u673A\u53F7\u7801",name:"Phone",rules:[{validator:function(){var f=A()(o()().mark(function Z(y,c){return o()().wrap(function(h){for(;;)switch(h.prev=h.next){case 0:if(c!=null){h.next=2;break}throw new Error("\u8BF7\u8F93\u5165\u624B\u673A\u53F7\u7801");case 2:case"end":return h.stop()}},Z)}));function t(Z,y){return f.apply(this,arguments)}return t}()}],children:(0,e.jsx)(se.Z,{name:"Phone",placeholder:"\u8BF7\u8F93\u5165\u624B\u673A\u53F7\u7801",style:{width:"250px"},showCount:!0})}),(0,e.jsx)(r.Z.Item,{label:"\u8D26\u6237\u72B6\u6001",name:"IsEnabled",style:{paddingLeft:"25px"},children:(0,e.jsx)(k.Z,{})})]}),(0,e.jsxs)(ce.Z,{children:[(0,e.jsx)(r.Z.Item,{label:"\u8BC1\u4EF6\u53F7\u7801",name:"IdCard",children:(0,e.jsx)(se.Z,{name:"IdCard",placeholder:"\u8BF7\u8F93\u5165\u8BC1\u4EF6\u53F7\u7801",style:{width:"250px"},showCount:!0})}),(0,e.jsx)(r.Z.Item,{style:{paddingLeft:"25px"},label:"\u6700\u9AD8\u6743\u9650",name:"IsAdmin",children:(0,e.jsx)(k.Z,{})}),(0,e.jsx)(r.Z.Item,{label:"\u7981\u6B62\u5FAE\u4FE1\u767B\u5F55",name:"DisableWxLogin",children:(0,e.jsx)(k.Z,{})}),(0,e.jsx)(r.Z.Item,{label:"\u7981\u6B62\u5FAE\u4FE1\u6D88\u606F",name:"DisableWxMessage",children:(0,e.jsx)(k.Z,{})})]}),(0,e.jsxs)(ce.Z,{children:[(0,e.jsx)(r.Z.Item,{label:"\u6240\u5C5E\u516C\u53F8",name:"CompanyName",children:(0,e.jsx)(se.Z,{placeholder:"\u8BF7\u8F93\u5165\u6240\u5C5E\u516C\u53F8",style:{width:"250px"}})}),(0,e.jsx)(r.Z.Item,{style:{paddingLeft:"25px"},label:"\u7528\u6237\u6027\u522B",name:"Sex",children:(0,e.jsx)(O.Z,{allowClear:!1,options:[{label:"\u672A\u9009\u62E9",value:0},{label:"\u7537",value:1},{label:"\u5973",value:2}],multiple:!1,title:"",placeholder:"",onChange:function(t){}})}),(0,e.jsx)(r.Z.Item,{name:"EntryTime",label:"\u5165\u804C\u65F6\u95F4",children:(0,e.jsx)(Ea.Z,{style:{width:100},noStyle:!0})})]}),he=="ExamSite"&&(0,e.jsx)(ce.Z,{children:(0,e.jsx)(r.Z.Item,{name:"CommissionRate",label:"\u5206\u6DA6\u6BD4\u4F8B",children:(0,e.jsx)(ve.Z,{placeholder:"\u8BF7\u8F93\u5165\u5206\u6DA6\u6BD4\u4F8B",style:{width:"250px"},max:1,min:0})})})]}),a.userId!=null&&(0,e.jsxs)(We.Z,{style:{textAlign:"right"},span:5,children:[(0,e.jsx)(sa.Z,{preview:!1,width:110,height:128,id:"zp",src:i==null?void 0:i.HeadImgUrl,fallback:"https://cdn.51panda.com/no-image.png"}),(0,e.jsx)(Ne.Z,ge()(ge()({},L),{},{children:(0,e.jsx)(de.ZP,{icon:(0,e.jsx)(la,{}),style:{marginTop:10},children:"\u4E0A\u4F20\u7167\u7247"})}))]})]}),he=="JiaXiao"&&(0,e.jsxs)(e.Fragment,{children:[(0,e.jsxs)(W.Z,{children:[(0,e.jsx)(r.Z.Item,{label:"\u62A5\u540D\u95E8\u5E97",name:"JxDeptId",children:(0,e.jsx)(O.Z,{allowClear:!1,request:_.eB,listHeight:600,multiple:!1,title:"",placeholder:"",onChange:function(t){},width:300})}),(0,e.jsx)("div",{style:{paddingLeft:"32px"},children:(0,e.jsx)(r.Z.Item,{label:"\u57F9\u8BAD\u573A\u5730",name:"JxFieldId",children:(0,e.jsx)(O.Z,{allowClear:!1,request:_.W9,listHeight:600,multiple:!1,title:"",placeholder:"",onChange:function(t){},width:300})})})]}),(0,e.jsx)(W.Z,{children:(0,e.jsx)(r.Z.Item,{label:"\u57F9\u8BAD\u79D1\u76EE",name:"KeMuIds",children:(0,e.jsx)(O.Z,{allowClear:!1,options:[{label:"\u79D1\u76EE\u4E8C",value:"2"},{label:"\u79D1\u76EE\u4E09",value:"3"},{label:"\u79D1\u76EE\u4E8C\u6A21\u62DF",value:"20"},{label:"\u79D1\u76EE\u4E09\u6A21\u62DF",value:"30"}],multiple:!0,title:"",placeholder:"",onChange:function(t){},width:500})})}),(0,e.jsx)($e.Z,{style:{},children:"\u64CD\u4F5C\u6743\u9650\u76F8\u5173"}),(0,e.jsx)(W.Z,{children:(0,e.jsx)(r.Z.Item,{label:"\u5173\u8054\u89D2\u8272",name:"RoleIds",children:(0,e.jsx)(O.Z,{allowClear:!1,request:be.KK,listHeight:600,multiple:!0,title:"",placeholder:"",maxTagCount:4,onChange:function(t){},width:800})})}),(0,e.jsxs)(W.Z,{children:[(0,e.jsx)(r.Z.Item,{label:"\u67E5\u8BE2\u62A5\u540D\u5E97\u9762\u6743\u9650",name:"SearchJxDeptIds",children:(0,e.jsx)(O.Z,{allowClear:!1,request:_.eB,listHeight:600,multiple:!0,title:"",placeholder:"\u9009\u62E9\u53EF\u4EE5\u67E5\u770B\u7684\u5E97\u9762",onChange:function(t){},width:300})}),(0,e.jsx)("div",{style:{paddingLeft:"25px"},children:(0,e.jsx)(r.Z.Item,{label:"\u67E5\u8BE2\u57F9\u8BAD\u573A\u5730\u6743\u9650",name:"SearchJxFieldIds",children:(0,e.jsx)(O.Z,{allowClear:!1,request:_.W9,listHeight:600,multiple:!0,title:"",placeholder:"\u9009\u62E9\u53EF\u4EE5\u67E5\u770B\u7684\u8BAD\u7EC3\u573A",onChange:function(t){},width:300})})})]}),(0,e.jsxs)(W.Z,{children:[(0,e.jsx)(r.Z.Item,{label:"\u53EF\u4EE5\u67E5\u8BE2\u6240\u6709\u7684\u62A5\u540D\u70B9\u548C\u8BAD\u7EC3\u573A\u7684\u5B66\u5458\u4FE1\u606F",name:"NoLockDeptAndField",children:(0,e.jsx)(k.Z,{})}),(0,e.jsx)("div",{style:{paddingLeft:"25px"},children:(0,e.jsx)(r.Z.Item,{label:"\u53EF\u4EE5\u5728\u6240\u6709\u7684\u62A5\u540D\u5E97\u9762\u6DFB\u52A0\u5B66\u5458\u4FE1\u606F",name:"NoLockAddDeptAndField",children:(0,e.jsx)(k.Z,{})})})]}),(0,e.jsxs)(W.Z,{children:[(0,e.jsx)(r.Z.Item,{label:"\u53EA\u80FD\u770B\u81EA\u5DF1\u63A8\u8350\u7684\u5B66\u5458",name:"LookOnlyMySaleStudent",children:(0,e.jsx)(k.Z,{})}),(0,e.jsx)("div",{style:{paddingLeft:"25px"},children:(0,e.jsx)(r.Z.Item,{label:"\u53EA\u80FD\u770B\u81EA\u5DF1\u5F55\u5165\u7684\u5B66\u5458",name:"LookOnlyMyCreateStudent",children:(0,e.jsx)(k.Z,{})})}),(0,e.jsx)("div",{style:{paddingLeft:"25px"},children:(0,e.jsx)(r.Z.Item,{label:"\u53EA\u80FD\u770B\u81EA\u5DF1\u63A8\u8350\u548C\u5F55\u5165\u7684\u5B66\u5458",name:"LookOnlyMySaleAndCreateStudent",children:(0,e.jsx)(k.Z,{})})}),(0,e.jsx)("div",{style:{paddingLeft:"25px"},children:(0,e.jsx)(r.Z.Item,{label:"\u62A5\u540D\u81EA\u52A8\u5206\u8F66",name:"AutoAssignCoach",children:(0,e.jsx)(k.Z,{})})})]}),(0,e.jsxs)(W.Z,{children:[(0,e.jsx)(r.Z.Item,{label:"\u67E5\u8BE2\u5929\u6570",name:"SearchDays",children:(0,e.jsx)(ve.Z,{placeholder:"\u5F53\u524D\u8D26\u53F7\u53EF\u4EE5\u67E5\u8BE2\u7684\u5929\u6570",style:{width:"200px"}})}),(0,e.jsx)("div",{style:{paddingLeft:"25px"},children:(0,e.jsx)(r.Z.Item,{label:"\u8865\u5F55\u5929\u6570",name:"MakeUpDays",children:(0,e.jsx)(ve.Z,{placeholder:"\u5F53\u524D\u8D26\u53F7\u53EF\u4EE5\u8865\u5F55\u7684\u5929\u6570",style:{width:"200px"}})})})]})]}),he=="ExamSite"&&(0,e.jsxs)(e.Fragment,{children:[(0,e.jsx)($e.Z,{style:{},children:"\u64CD\u4F5C\u6743\u9650\u76F8\u5173"}),(0,e.jsx)(W.Z,{children:(0,e.jsx)(r.Z.Item,{label:"\u5173\u8054\u89D2\u8272",name:"RoleIds",children:(0,e.jsx)(O.Z,{allowClear:!1,request:be.KK,listHeight:600,multiple:!0,title:"",placeholder:"",maxTagCount:4,onChange:function(t){},width:800})})}),(0,e.jsxs)(W.Z,{children:[(0,e.jsx)(r.Z.Item,{label:"\u67E5\u8BE2\u5929\u6570",name:"SearchDays",children:(0,e.jsx)(ve.Z,{placeholder:"\u5F53\u524D\u8D26\u53F7\u53EF\u4EE5\u67E5\u8BE2\u7684\u5929\u6570",style:{width:"200px"}})}),(0,e.jsx)("div",{style:{paddingLeft:"25px"},children:(0,e.jsx)(r.Z.Item,{label:"\u57F9\u8BAD\u79D1\u76EE",name:"KeMuIds",children:(0,e.jsx)(O.Z,{allowClear:!1,options:[{label:"\u79D1\u76EE\u4E8C",value:2},{label:"\u79D1\u76EE\u4E09",value:3},{label:"\u79D1\u76EE\u4E8C\u6A21\u62DF",value:20},{label:"\u79D1\u76EE\u4E09\u6A21\u62DF",value:30}],multiple:!0,title:"",placeholder:"",maxTagCount:4,onChange:function(t){},width:500})})})]})]})]},"userinfo-1-form-"+te);Ze(g)},p=i?[{key:"userinfo-1",label:"\u57FA\u672C\u4FE1\u606F",children:je},{key:"userinfo-2",label:"\u5206\u7C7B\u7BA1\u7406",forceRender:!0,children:d!=null&&(0,e.jsxs)(e.Fragment,{children:[d!=null&&d.length==0&&(0,e.jsx)(ia.ZP,{status:"500",title:"500",subTitle:"\u7CFB\u7EDF\u6682\u672A\u914D\u7F6E\u76F8\u5E94\u7684\u5206\u7C7B\uFF0C\u5982\u9700\u8981\u524D\u5F80\u5206\u7C7B\u7BA1\u7406\u8FDB\u884C\u914D\u7F6E."}),d!=null&&d.map(function(m,g){return(0,e.jsxs)(r.Z,{initialValues:i,form:H,onFinish:function(t){V(!0);var Z=Object.keys(t).filter(function(c){return c.startsWith("CategoryIds-")}).map(function(c){return t[c]}).filter(function(c){return Array.isArray(c)&&c.length>0}).reduce(function(c,E){return c.concat(E)},[]),y=aa()(new Set(Z));(0,N.WY)("/SystemManage/Category/setUserCategoryInfo",{method:"PUT",data:{CategoryIds:y,Id:a.userId}}).then(function(c){V(!1),c.success&&a.messageApi.success(c.message)})},children:[(0,e.jsx)(r.Z.Item,{name:"CategoryIds-"+m.Id,hidden:!0,initialValue:m.values},"CategoryIds-"+m.Id+"-"+te),(0,e.jsx)(r.Z.Item,{label:m.Name,children:(0,e.jsx)(oa.Z,{allowClear:!0,defaultValue:m.values,treeCheckable:!0,treeCheckStrictly:!0,treeDefaultExpandAll:!0,treeData:d[g].children,multiple:!0,title:"",placeholder:"\u8BF7\u786E\u8BA4\u9009\u62E9\u4E0A\u7EA7\u76EE\u5F55\uFF0C\u5982\u4E0D\u9009\u62E9\uFF0C\u5F53\u524D\u5206\u7C7B\u4F1A\u8BBE\u7F6E\u6210\u6839\u76EE\u5F55",maxTagCount:4,style:{width:"800px"},listHeight:600,fieldNames:{value:"Id",label:"Name",children:"children"},onChange:function(t){H.setFieldValue("CategoryIds-"+m.Id,t.map(function(Z){return Z.value}))}})})]},"userinfo-1-form-"+te)})]})}]:[],Q=!i||a.userId==null?[]:[{key:"userinfo-3",label:"\u62A5\u540D\u4EF7\u683C",forceRender:!0,children:(0,e.jsx)(e.Fragment,{children:(0,e.jsx)(ya,{userId:a.userId,ref:ee,onSubmitCallback:function(){V(!1)}})})},{key:"userinfo-4",label:"\u64CD\u4F5C\u65E5\u5FD7",forceRender:!0,children:(0,e.jsx)(e.Fragment,{children:(0,e.jsx)(Fa,{userId:a.userId})})},{key:"userinfo-5",label:"\u4FEE\u6539\u65E5\u5FD7",forceRender:!0,children:(0,e.jsx)(e.Fragment,{children:(0,e.jsx)(Za,{userId:a.userId})})}];return(0,e.jsxs)(e.Fragment,{children:[ae&&(0,e.jsxs)(q.Z,{onOk:function(){Y=="userinfo-1"?I.submit():Y=="userinfo-2"?H.submit():Y=="userinfo-3"&&(V(!0),ee.current.submit())},open:ae,onCancel:function(){I.resetFields(),j(!1)},loading:J,confirmLoading:T,destroyOnClose:!0,title:i==null?"\u52A0\u8F7D\u4E2D...":a.userId!=null?i.RealName+" \u8D26\u6237\u7F16\u8F91":"\u521B\u5EFA\u65B0\u8D26\u6237",width:1200,children:[a.userId==null&&(0,e.jsx)(e.Fragment,{children:je}),a.userId!=null&&(0,e.jsx)(da.Z,{activeKey:Y,onChange:function(g){Ue(g)},className:"no-padding-2",items:(i==null?void 0:i.TenantType)=="JiaXiao"?p.concat(Q):p},"userinfo")]}),(0,e.jsx)("a",{onClick:function(){Pe(te+1),xe(void 0),M(void 0),s()},children:a.realName})]})}),Je=Aa,Ke=n(42509),Sa=n(1291),Da=function(S){var D=S.open,C=S.onCancel,I=(0,l.useRef)();return(0,l.useEffect)(function(){D&&I.current&&I.current.open()},[D]),(0,e.jsx)(Oe.default,{ref:I,formItems:[{type:"tab",width:"100%",tabPosition:"top",defaultActiveKey:"audit",tabItems:[{key:"audit",label:"\u9700\u8981\u5BA1\u6838",children:[{type:"imagebase64",name:"",src:"/Auth/CreateUserCode/getAuditRegisterQCode",width:200,height:200,preview:!1,initPostData:void 0}]},{key:"direct",label:"\u76F4\u63A5\u6CE8\u518C",children:[{type:"imagebase64",name:"",src:"/Auth/CreateUserCode/getDirectRegisterQCode",width:200,height:200,preview:!1,initPostData:void 0}]}]}],modifyTitle:"\u626B\u7801\u6CE8\u518C",insertTitle:"\u626B\u7801\u6CE8\u518C",width:500,onCallBack:function(){},getPath:"",setPath:"",hideFooter:!0,onOpenChange:function($){$||C()}})},ba=Da,Ba=function(){var S=ue.ZP.useMessage(),D=v()(S,2),C=D[0],I=D[1],B=(0,l.useRef)(),$=(0,l.useRef)(null),H=(0,l.useRef)(null),ee=function(s){return(0,e.jsx)(Je,{userId:s.Id,realName:s.RealName,messageApi:C})},fe=function(s){return(0,e.jsx)(Xe,{userId:s.Id})},ie=function(s){return(0,e.jsx)(Ge.Z,{userId:s.Id,realName:s.RealName})},ae=function(s){return(0,e.jsx)(_e,{userId:s.Id,realName:s.RealName,isEnabled:s.IsEnabled,onCallBack:function(){B.current.reload()},messageApi:C})},j=l.useState([]),w=v()(j,2),b=w[0],J=w[1],oe=l.useState([]),z=v()(oe,2),K=z[0],T=z[1],V=(0,l.useState)(!1),U=v()(V,2),R=U[0],i=U[1],M=(0,l.useState)(!1),P=v()(M,2),ne=P[0],d=P[1],xe=(0,l.useState)([]),pe=v()(xe,2),ye=pe[0],Y=pe[1],Ue=(0,l.useState)([]),Ie=v()(Ue,2),Re=Ie[0],te=Ie[1],Pe=[{key:"enable",label:"\u6279\u91CF\u542F\u7528",onClick:function(s,L){var F,p=0,Q=s.length;F=q.Z.confirm({title:"\u786E\u8BA4\u64CD\u4F5C",content:"\u786E\u8BA4\u8981\u542F\u7528\u9009\u4E2D\u7684 ".concat(s.length," \u4E2A\u7528\u6237\u5417\uFF1F"),okButtonProps:{loading:!1},onOk:function(){var m=A()(o()().mark(function f(){var t;return o()().wrap(function(y){for(;;)switch(y.prev=y.next){case 0:return F.update({title:"\u6B63\u5728\u6279\u91CF\u542F\u7528\u7528\u6237",content:"\u5904\u7406\u8FDB\u5EA6\uFF1A".concat(p,"/").concat(Q),okButtonProps:{style:{display:"none"}},cancelButtonProps:{style:{display:"none"}}}),t=function(){var c=A()(o()().mark(function E(h){return o()().wrap(function(u){for(;;)switch(u.prev=u.next){case 0:if(!(h>=s.length)){u.next=7;break}return F.destroy(),C.success("\u6279\u91CF\u542F\u7528\u5B8C\u6210\uFF01"),Y([]),te([]),B.current.reload(),u.abrupt("return");case 7:return u.prev=7,u.next=10,(0,N.WY)("/SystemManage/User/openUser",{method:"PUT",data:{id:s[h]}});case 10:return p++,F.update({content:"\u5904\u7406\u8FDB\u5EA6\uFF1A".concat(p,"/").concat(Q)}),u.next=14,t(h+1);case 14:u.next=20;break;case 16:u.prev=16,u.t0=u.catch(7),F.destroy(),C.error("\u64CD\u4F5C\u8FC7\u7A0B\u4E2D\u51FA\u73B0\u9519\u8BEF");case 20:case"end":return u.stop()}},E,null,[[7,16]])}));return function(h){return c.apply(this,arguments)}}(),y.next=4,t(0);case 4:case"end":return y.stop()}},f)}));function g(){return m.apply(this,arguments)}return g}()})}},{key:"disable",label:"\u6279\u91CF\u7981\u7528",onClick:function(s,L){var F,p=0,Q=s.length;F=q.Z.confirm({title:"\u786E\u8BA4\u64CD\u4F5C",content:"\u786E\u8BA4\u8981\u7981\u7528\u9009\u4E2D\u7684 ".concat(s.length," \u4E2A\u7528\u6237\u5417\uFF1F"),okButtonProps:{loading:!1},onOk:function(){var m=A()(o()().mark(function f(){var t;return o()().wrap(function(y){for(;;)switch(y.prev=y.next){case 0:return F.update({title:"\u6B63\u5728\u6279\u91CF\u7981\u7528\u7528\u6237",content:"\u5904\u7406\u8FDB\u5EA6\uFF1A".concat(p,"/").concat(Q),okButtonProps:{style:{display:"none"}},cancelButtonProps:{style:{display:"none"}}}),t=function(){var c=A()(o()().mark(function E(h){return o()().wrap(function(u){for(;;)switch(u.prev=u.next){case 0:if(!(h>=s.length)){u.next=7;break}return F.destroy(),C.success("\u6279\u91CF\u7981\u7528\u5B8C\u6210\uFF01"),Y([]),te([]),B.current.reload(),u.abrupt("return");case 7:return u.prev=7,u.next=10,(0,N.WY)("/SystemManage/User/closeUser",{method:"PUT",data:{id:s[h]}});case 10:return p++,F.update({content:"\u5904\u7406\u8FDB\u5EA6\uFF1A".concat(p,"/").concat(Q)}),u.next=14,t(h+1);case 14:u.next=20;break;case 16:u.prev=16,u.t0=u.catch(7),F.destroy(),C.error("\u64CD\u4F5C\u8FC7\u7A0B\u4E2D\u51FA\u73B0\u9519\u8BEF");case 20:case"end":return u.stop()}},E,null,[[7,16]])}));return function(h){return c.apply(this,arguments)}}(),y.next=4,t(0);case 4:case"end":return y.stop()}},f)}));function g(){return m.apply(this,arguments)}return g}()})}}],Le={selectedRowKeys:ye,setSelectedRowKeys:Y,onChange:function(s,L){Y(s),te(L)}};(0,l.useEffect)(function(){var x=function(){var s=A()(o()().mark(function L(){return o()().wrap(function(p){for(;;)switch(p.prev=p.next){case 0:(0,N.WY)("/SystemManage/Category/getCategoryList",{method:"POST"}).then(function(Q){var m=[];Q.data.map(function(g){m.push({dataIndex:"Column-".concat(g.Id),title:g.Name,align:"center",search:!1})}),console.log("_columns:"+m),T(m),J([{title:"\u5E8F\u53F7",dataIndex:"RowIndex",align:"center"},{title:"\u7F16\u53F7",dataIndex:"SysId",align:"center"},{title:"\u8D26\u53F7",dataIndex:"Account",align:"center"},{title:"\u540D\u5B57",dataIndex:"RealName",render:function(f,t){return ee(t)},align:"center"},{title:"\u7535\u8BDD",dataIndex:"Phone",align:"center"},{title:"\u8F66\u724C",dataIndex:"CarNumbers",align:"center"},{title:"\u8BC1\u4EF6\u53F7\u7801",dataIndex:"Idcard",align:"center"},{title:"\u5165\u804C\u65E5\u671F",dataIndex:"EntryTime",align:"center",IsDate:!0},{title:"\u5BC6\u7801",render:function(f,t){return fe(t)},align:"center"},{title:"\u5FAE\u4FE1",render:function(f,t){return ie(t)},align:"center"},{title:"\u72B6\u6001",render:function(f,t){return ae(t)},align:"center"},{title:"\u516C\u53F8\u540D\u79F0",dataIndex:"CompanyName",align:"center"},{title:"\u57F9\u8BAD\u573A\u5730",dataIndex:"JxDeptName",align:"center"},{title:"\u8BAD\u7EC3\u573A\u5730",dataIndex:"JxFieldName",align:"center"}].concat(m,[{title:"\u521B\u5EFA\u4EBA",dataIndex:"CreateUserName",align:"center"},{title:"\u521B\u5EFA\u65F6\u95F4",dataIndex:"CreateTime",align:"center"}]))});case 1:case"end":return p.stop()}},L)}));return function(){return s.apply(this,arguments)}}();x()},[]);var Fe=[{name:"SearchKey",type:"input",label:"\u5173\u952E\u5B57\u8BCD",placeholder:"\u8BF7\u8F93\u5165\u540D\u5B57\u3001\u8D26\u53F7\u3001\u7535\u8BDD\u7B49",allowClear:!0},{name:"IsEnableds",type:"select",label:"\u7528\u6237\u72B6\u6001",placeholder:"\u8BF7\u9009\u62E9\u7528\u6237\u72B6\u6001",options:[{label:"\u6709\u6548",value:"1"},{label:"\u65E0\u6548",value:"0"}],value:["1"],multiple:!0,allowClear:!0},{name:"IsAdmins",type:"select",label:"\u6700\u9AD8\u6743\u9650",placeholder:"\u9009\u62E9\u662F\u5426\u6709\u6700\u9AD8\u6743\u9650\u7684",options:[{label:"\u662F",value:"1"},{label:"\u5426",value:"0"}],allowClear:!0,onChange:function(){}},{name:"RoleId",type:"select",label:"\u7528\u6237\u89D2\u8272",placeholder:"\u9009\u62E9\u7528\u6237\u89D2\u8272\u6765\u67E5\u8BE2",request:be.KK,allowClear:!0,onChange:function(){}},{name:"JxDeptIds",type:"select",label:"\u6240\u5C5E\u95E8\u5E97",placeholder:"\u9009\u62E9\u7528\u6237\u6240\u5C5E\u95E8\u5E97",request:_.eB,multiple:!0,allowClear:!0,onChange:function(){}},{name:"JxFieldIds",type:"select",label:"\u6240\u5C5E\u573A\u5730",placeholder:"\u9009\u62E9\u7528\u6237\u6240\u5C5E\u573A\u5730",request:_.W9,allowClear:!0,onChange:function(){}},{name:"CategoryId",type:"selecttree",label:"\u7528\u6237\u5206\u7C7B",placeholder:"\u9009\u62E9\u7528\u6237\u5206\u7C7B",request:Be.mC,maxTagCount:1,allowClear:!0,onChange:function(s){B.current.setFormData("CategoryId",s)}},{name:"CategoryParentId",type:"selecttree",label:"\u5206\u7C7B\u4E0A\u7EA7",placeholder:"\u9009\u62E9\u7528\u6237\u5206\u7C7B\u4E0A\u7EA7",request:Be.mC,maxTagCount:1,allowClear:!0,onChange:function(s){B.current.setFormData("CategoryParentId",s)}}],je=function(){i(!1),d(!0)},Ze=function(){d(!1)},Me=function(){i(!0)},Ee=function(){i(!1)},he=[(0,e.jsx)(de.ZP,{icon:(0,e.jsx)(Ke.Z,{}),type:"primary",onClick:Me,ghost:!0,children:"\u6DFB\u52A0"},"add-button"),(0,e.jsx)(q.Z,{title:"\u9009\u62E9\u6DFB\u52A0\u65B9\u5F0F",open:R,onCancel:Ee,footer:null,centered:!0,children:(0,e.jsxs)("div",{style:{display:"flex",justifyContent:"space-around",padding:"20px"},children:[(0,e.jsx)(de.ZP,{type:"primary",icon:(0,e.jsx)(Ke.Z,{}),onClick:function(){H.current.open(),i(!1)},size:"large",style:{height:"45px",width:"140px",fontSize:"16px"},children:"\u76F4\u63A5\u6DFB\u52A0"}),(0,e.jsx)(de.ZP,{type:"primary",icon:(0,e.jsx)(Sa.Z,{}),onClick:je,size:"large",style:{height:"45px",width:"140px",fontSize:"16px"},children:"\u626B\u7801\u6CE8\u518C"})]})},"add-modal")];return(0,e.jsxs)(re._z,{header:{breadcrumb:{},title:""},children:[I,b.length>0&&(0,e.jsx)(le.Z,{ref:B,formCols:Fe,columns:b,tableButtons:he,getPath:"/SystemManage/User/getUserList",downloadPath:"/SystemManage/User/exportUserList",downloadTableName:"UserList",rowKey:"Id",rowSelection:Le,batchOperations:Pe,transformedData:function(s){return s.map(function(L){if(L.CategoryDatas&&Array.isArray(L.CategoryDatas)){var F={};L.CategoryDatas.forEach(function(p){F[p.CategoryId]||(F[p.CategoryId]=[]),F[p.CategoryId].push(p.CategoryName)}),Object.keys(F).forEach(function(p){L["Column-".concat(p)]=F[p].join(", ")})}}),s}},"my-list-table"),(0,e.jsx)(ze.Z,{ref:$,StudentListRef:void 0,updateAddLoading:void 0}),(0,e.jsx)(Je,{userId:void 0,realName:void 0,ref:H,messageApi:C}),(0,e.jsx)(ba,{open:ne,onCancel:Ze})]})},wa=Ba}}]);
