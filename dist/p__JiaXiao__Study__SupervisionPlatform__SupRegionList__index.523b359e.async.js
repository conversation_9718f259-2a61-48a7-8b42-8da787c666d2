"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[9465],{25930:function(F){(function(h,n){F.exports=n()})(this,function(){function h(e){var l=[];return e.AMapUI&&l.push(n(e.AMapUI)),e.Loca&&l.push(_(e.Loca)),Promise.all(l)}function n(e){return new Promise(function(l,o){var u=[];if(e.plugins)for(var i=0;i<e.plugins.length;i+=1)s.AMapUI.plugins.indexOf(e.plugins[i])==-1&&u.push(e.plugins[i]);if(r.AMapUI===a.failed)o("\u524D\u6B21\u8BF7\u6C42 AMapUI \u5931\u8D25");else if(r.AMapUI===a.notload){r.AMapUI=a.loading,s.AMapUI.version=e.version||s.AMapUI.version,i=s.AMapUI.version;var f=document.body||document.head,p=document.createElement("script");p.type="text/javascript",p.src="https://webapi.amap.com/ui/"+i+"/main.js",p.onerror=function(t){r.AMapUI=a.failed,o("\u8BF7\u6C42 AMapUI \u5931\u8D25")},p.onload=function(){if(r.AMapUI=a.loaded,u.length)window.AMapUI.loadUI(u,function(){for(var t=0,P=u.length;t<P;t++){var E=u[t].split("/").slice(-1)[0];window.AMapUI[E]=arguments[t]}for(l();A.AMapUI.length;)A.AMapUI.splice(0,1)[0]()});else for(l();A.AMapUI.length;)A.AMapUI.splice(0,1)[0]()},f.appendChild(p)}else r.AMapUI===a.loaded?e.version&&e.version!==s.AMapUI.version?o("\u4E0D\u5141\u8BB8\u591A\u4E2A\u7248\u672C AMapUI \u6DF7\u7528"):u.length?window.AMapUI.loadUI(u,function(){for(var t=0,P=u.length;t<P;t++){var E=u[t].split("/").slice(-1)[0];window.AMapUI[E]=arguments[t]}l()}):l():e.version&&e.version!==s.AMapUI.version?o("\u4E0D\u5141\u8BB8\u591A\u4E2A\u7248\u672C AMapUI \u6DF7\u7528"):A.AMapUI.push(function(t){t?o(t):u.length?window.AMapUI.loadUI(u,function(){for(var P=0,E=u.length;P<E;P++){var w=u[P].split("/").slice(-1)[0];window.AMapUI[w]=arguments[P]}l()}):l()})})}function _(e){return new Promise(function(l,o){if(r.Loca===a.failed)o("\u524D\u6B21\u8BF7\u6C42 Loca \u5931\u8D25");else if(r.Loca===a.notload){r.Loca=a.loading,s.Loca.version=e.version||s.Loca.version;var u=s.Loca.version,i=s.AMap.version.startsWith("2"),f=u.startsWith("2");if(i&&!f||!i&&f)o("JSAPI \u4E0E Loca \u7248\u672C\u4E0D\u5BF9\u5E94\uFF01\uFF01");else{i=s.key,f=document.body||document.head;var p=document.createElement("script");p.type="text/javascript",p.src="https://webapi.amap.com/loca?v="+u+"&key="+i,p.onerror=function(t){r.Loca=a.failed,o("\u8BF7\u6C42 AMapUI \u5931\u8D25")},p.onload=function(){for(r.Loca=a.loaded,l();A.Loca.length;)A.Loca.splice(0,1)[0]()},f.appendChild(p)}}else r.Loca===a.loaded?e.version&&e.version!==s.Loca.version?o("\u4E0D\u5141\u8BB8\u591A\u4E2A\u7248\u672C Loca \u6DF7\u7528"):l():e.version&&e.version!==s.Loca.version?o("\u4E0D\u5141\u8BB8\u591A\u4E2A\u7248\u672C Loca \u6DF7\u7528"):A.Loca.push(function(t){t?o(t):o()})})}if(!window)throw Error("AMap JSAPI can only be used in Browser.");var a;(function(e){e.notload="notload",e.loading="loading",e.loaded="loaded",e.failed="failed"})(a||(a={}));var s={key:"",AMap:{version:"1.4.15",plugins:[]},AMapUI:{version:"1.1",plugins:[]},Loca:{version:"1.3.2"}},r={AMap:a.notload,AMapUI:a.notload,Loca:a.notload},A={AMap:[],AMapUI:[],Loca:[]},M=[],g=function(e){typeof e=="function"&&(r.AMap===a.loaded?e(window.AMap):M.push(e))};return{load:function(e){return new Promise(function(l,o){if(r.AMap==a.failed)o("");else if(r.AMap==a.notload){var u=e.key,i=e.version,f=e.plugins;u?(window.AMap&&location.host!=="lbs.amap.com"&&o("\u7981\u6B62\u591A\u79CDAPI\u52A0\u8F7D\u65B9\u5F0F\u6DF7\u7528"),s.key=u,s.AMap.version=i||s.AMap.version,s.AMap.plugins=f||s.AMap.plugins,r.AMap=a.loading,i=document.body||document.head,window.___onAPILoaded=function(t){if(delete window.___onAPILoaded,t)r.AMap=a.failed,o(t);else for(r.AMap=a.loaded,h(e).then(function(){l(window.AMap)}).catch(o);M.length;)M.splice(0,1)[0]()},f=document.createElement("script"),f.type="text/javascript",f.src="https://webapi.amap.com/maps?callback=___onAPILoaded&v="+s.AMap.version+"&key="+u+"&plugin="+s.AMap.plugins.join(","),f.onerror=function(t){r.AMap=a.failed,o(t)},i.appendChild(f)):o("\u8BF7\u586B\u5199key")}else if(r.AMap==a.loaded)if(e.key&&e.key!==s.key)o("\u591A\u4E2A\u4E0D\u4E00\u81F4\u7684 key");else if(e.version&&e.version!==s.AMap.version)o("\u4E0D\u5141\u8BB8\u591A\u4E2A\u7248\u672C JSAPI \u6DF7\u7528");else{if(u=[],e.plugins)for(i=0;i<e.plugins.length;i+=1)s.AMap.plugins.indexOf(e.plugins[i])==-1&&u.push(e.plugins[i]);u.length?window.AMap.plugin(u,function(){h(e).then(function(){l(window.AMap)}).catch(o)}):h(e).then(function(){l(window.AMap)}).catch(o)}else if(e.key&&e.key!==s.key)o("\u591A\u4E2A\u4E0D\u4E00\u81F4\u7684 key");else if(e.version&&e.version!==s.AMap.version)o("\u4E0D\u5141\u8BB8\u591A\u4E2A\u7248\u672C JSAPI \u6DF7\u7528");else{var p=[];if(e.plugins)for(i=0;i<e.plugins.length;i+=1)s.AMap.plugins.indexOf(e.plugins[i])==-1&&p.push(e.plugins[i]);g(function(){p.length?window.AMap.plugin(p,function(){h(e).then(function(){l(window.AMap)}).catch(o)}):h(e).then(function(){l(window.AMap)}).catch(o)})}})},reset:function(){delete window.AMap,delete window.AMapUI,delete window.Loca,s={key:"",AMap:{version:"1.4.15",plugins:[]},AMapUI:{version:"1.1",plugins:[]},Loca:{version:"1.3.2"}},r={AMap:a.notload,AMapUI:a.notload,Loca:a.notload},A={AMap:[],AMapUI:[],Loca:[]}}}})},16894:function(F,h,n){var _=n(17014);h.ZP=_.Z},52377:function(F,h,n){n.r(h);var _=n(15009),a=n.n(_),s=n(99289),r=n.n(s),A=n(5574),M=n.n(A),g=n(67294),e=n(45360),l=n(83622),o=n(36447),u=n(11774),i=n(16894),f=n(78158),p=n(37476),t=n(80853),P=n(25930),E=n.n(P),w=n(5892),d=n(85893);t.vc.version="2.0",t.vc.key="12fe00b51a9b8c68678bee516e734ef7";var x=function(){var j=e.ZP.useMessage(),L=M()(j,2),U=L[0],z=L[1],V=g.useRef(),S=g.useState(!1),J=M()(S,2),G=J[0],R=J[1],X=g.useRef(),Y=g.useState([]),T=M()(Y,2),Q=T[0],O=T[1],B=g.useState(),W=M()(B,2),k=W[0],K=W[1],q=g.useState(),$=M()(q,2),ee=$[0],ne=$[1],ae=function(m,c,v){if(m==null)e.ZP.error("\u5F53\u524D\u65E0\u6570\u636E").then(function(H){});else{var y=m.split(";"),I=[];if(y.forEach(function(H){var Z=H.split(","),oe=parseFloat(Z[0]),se=parseFloat(Z[1]);I.push([oe,se])}),O(I),R(!0),c==null)K(I[0]);else{var N=c.split(",");K([N[0],N[1]])}ne(v)}},te=g.useState(!1),ie=M()(te,2),pe=ie[0],ue=ie[1],ce=g.useRef(),fe=g.useState(!1),re=M()(fe,2),_e=re[0],le=re[1],ve=g.useState(),de=M()(ve,2),Me=de[0],ge=de[1],me=function(){var C=r()(a()().mark(function m(){return a()().wrap(function(v){for(;;)switch(v.prev=v.next){case 0:return le(!0),U.loading({content:"\u6B63\u5728\u52A0\u8F7D\u4FE1\u606F",key:"loading",duration:0}),v.next=4,(0,f.ZP)("/JiaXiao/Study/SupervisionPlatform/SupRegion/getALlSupRegionList",{method:"POST",data:{}}).then(function(y){le(!1),U.destroy("loading"),y&&y.success&&(ue(!0),ge(y.data))});case 4:case"end":return v.stop()}},m)}));return function(){return C.apply(this,arguments)}}();return(0,d.jsxs)(d.Fragment,{children:[z,(0,d.jsxs)(u._z,{header:{breadcrumb:{},title:""},children:[(0,d.jsx)(i.ZP,{cardBordered:!0,scroll:{x:"100%"},actionRef:V,columns:[{valueType:"indexBorder",dataIndex:"Index",title:"\u5E8F\u53F7",fixed:"left",align:"center",width:50,hideInSearch:!1},{dataIndex:"SearchKey",title:"\u5173\u952E\u5B57\u8BCD",hideInTable:!0},{dataIndex:"Id",title:"\u7F16\u53F7",align:"center",ellipsis:!0,width:50,hideInSearch:!0,render:function(m,c){return[(0,d.jsx)("a",{onClick:function(){ae(c.Polygon,c.CentralPoint,c.Color)},title:"\u67E5\u770B",children:c.Id},"edit")]}},{dataIndex:"CompanyShortName",title:"\u57F9\u8BAD\u673A\u6784",align:"center",ellipsis:!0,width:100,hideInSearch:!0},{dataIndex:"Name",title:"\u6559\u5B66\u533A\u57DF",align:"left",ellipsis:!0,width:150,hideInSearch:!0},{dataIndex:"Type",title:"\u6559\u5B66\u7C7B\u578B",align:"center",ellipsis:!0,width:80,hideInSearch:!0,render:function(m,c){return[c.Type==1?"\u7B2C\u4E8C\u90E8\u5206":c.Type==2?"\u7B2C\u4E09\u90E8\u5206":""]}},{dataIndex:"Address",title:"\u6559\u5B66\u533A\u57DF\u5730\u5740",align:"left",ellipsis:!0,width:200,hideInSearch:!0},{dataIndex:"CarType",title:"\u8F66\u578B",align:"center",ellipsis:!0,width:100,hideInSearch:!0},{title:"\u521B\u5EFA\u65F6\u95F4",dataIndex:"CreateTime",align:"center",width:180,ellipsis:!0,hideInSearch:!0}],request:r()(a()().mark(function C(){var m,c,v=arguments;return a()().wrap(function(I){for(;;)switch(I.prev=I.next){case 0:return m=v.length>0&&v[0]!==void 0?v[0]:{},I.next=3,(0,f.ZP)("/JiaXiao/Study/SupervisionPlatform/SupRegion/getSupRegionList",{method:"POST",data:m});case 3:return c=I.sent,I.abrupt("return",{success:c.success,data:c.data.data,total:c.data.total});case 5:case"end":return I.stop()}},C)})),rowKey:"Id",pagination:{showSizeChanger:!0,showQuickJumper:!0,defaultPageSize:10},headerTitle:"\u7535\u5B50\u56F4\u680F",toolBarRender:function(){return[(0,d.jsx)(l.ZP,{type:"primary",disabled:_e,icon:(0,d.jsx)(w.Z,{}),onClick:function(){me().then(function(c){})},children:"\u663E\u793A\u5168\u90E8"},"add")]}},"Id"),(0,d.jsx)(p.Y,{width:"80vw",layout:"horizontal",title:"\u7535\u5B50\u56F4\u680F\u663E\u793A",formRef:X,open:G,onOpenChange:R,autoFocusFirstInput:!0,modalProps:{destroyOnClose:!0},submitter:!1,children:(0,d.jsx)("div",{style:{width:"100%",height:"80vh"},children:(0,d.jsxs)(t.wi,{zoom:18,center:k,viewMode:"3D",children:[(0,d.jsx)(t.mg,{path:Q,fillColor:ee}),(0,d.jsx)(t.VS,{position:{top:"10px",left:"10px"}}),(0,d.jsx)(t.Ci,{position:"LB"}),(0,d.jsx)(t.o8,{position:{top:"110px",left:"40px"}})]})})}),(0,d.jsx)(p.Y,{width:"80vw",layout:"horizontal",title:"\u5168\u90E8\u7535\u5B50\u56F4\u680F\u663E\u793A",formRef:ce,open:pe,onOpenChange:ue,autoFocusFirstInput:!0,modalProps:{destroyOnClose:!0},onInit:function(){E().load({key:t.vc.key,version:t.vc.version,plugins:[]}).then(function(m){var c=new m.Map("bigMap",{center:[112.93495,28.228313],viewMode:"3D",zoom:14});Me.map(function(v){if(v.Polygon!=null){var y=v.Polygon.split(";"),I=[];y.forEach(function(H){var Z=H.split(","),oe=parseFloat(Z[0]),se=parseFloat(Z[1]);I.push([oe,se])});var N=new m.Polygon({path:I,fillColor:v.Color});c.add(N)}})})},submitter:!1,children:(0,d.jsx)("div",{style:{width:"100%",height:"80vh"},id:"bigMap"})})]})]})},b=function(){return(0,d.jsx)(o.Z,{children:(0,d.jsx)(x,{})})};h.default=b},36447:function(F,h,n){n.d(h,{Z:function(){return d}});var _=n(67294),a=n(93967),s=n.n(a),r=n(27288),A=n(53124),M=n(16474),g=n(94423),e=n(48311),l=n(66968),o=n(83559);const u=x=>{const{componentCls:b,colorText:D,fontSize:j,lineHeight:L,fontFamily:U}=x;return{[b]:{color:D,fontSize:j,lineHeight:L,fontFamily:U,[`&${b}-rtl`]:{direction:"rtl"}}}},i=()=>({});var f=(0,o.I$)("App",u,i),t=x=>{const{prefixCls:b,children:D,className:j,rootClassName:L,message:U,notification:z,style:V,component:S="div"}=x,{direction:J,getPrefixCls:G}=(0,_.useContext)(A.E_),R=G("app",b),[X,Y,T]=f(R),Q=s()(Y,R,j,L,T,{[`${R}-rtl`]:J==="rtl"}),O=(0,_.useContext)(l.J),B=_.useMemo(()=>({message:Object.assign(Object.assign({},O.message),U),notification:Object.assign(Object.assign({},O.notification),z)}),[U,z,O.message,O.notification]),[W,k]=(0,M.Z)(B.message),[K,q]=(0,e.Z)(B.notification),[$,ee]=(0,g.Z)(),ne=_.useMemo(()=>({message:W,notification:K,modal:$}),[W,K,$]);(0,r.ln)("App")(!(T&&S===!1),"usage","When using cssVar, ensure `component` is assigned a valid React component string.");const ae=S===!1?_.Fragment:S,te={className:Q,style:V};return X(_.createElement(l.Z.Provider,{value:ne},_.createElement(l.J.Provider,{value:B},_.createElement(ae,Object.assign({},S===!1?void 0:te),ee,k,q,D))))},E=()=>_.useContext(l.Z);const w=t;w.useApp=E;var d=w}}]);
