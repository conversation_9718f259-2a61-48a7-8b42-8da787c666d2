"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[4058],{88:function(W,a,s){s.r(a);var e=s(5574),d=s.n(e),p=s(11774),m=s(45360),u=s(36447),x=s(67294),n=s(85893),g=function(){var C=m.ZP.useMessage(),o=d()(C,2),O=o[0],j=o[1];return(0,n.jsxs)(n.Fragment,{children:[j,(0,n.jsx)(p._z,{})]})},t=function(){return(0,n.jsx)(u.Z,{children:(0,n.jsx)(g,{})})};a.default=t},36447:function(W,a,s){s.d(a,{Z:function(){return R}});var e=s(67294),d=s(93967),p=s.n(d),m=s(27288),u=s(53124),x=s(16474),n=s(94423),g=s(48311),t=s(66968),f=s(83559);const C=v=>{const{componentCls:i,colorText:A,fontSize:P,lineHeight:E,fontFamily:l}=v;return{[i]:{color:A,fontSize:P,lineHeight:E,fontFamily:l,[`&${i}-rtl`]:{direction:"rtl"}}}},o=()=>({});var O=(0,f.I$)("App",C,o),I=v=>{const{prefixCls:i,children:A,className:P,rootClassName:E,message:l,notification:y,style:Z,component:r="div"}=v,{direction:K,getPrefixCls:H}=(0,e.useContext)(u.E_),M=H("app",i),[N,S,h]=O(M),$=p()(S,M,P,E,h,{[`${M}-rtl`]:K==="rtl"}),c=(0,e.useContext)(t.J),_=e.useMemo(()=>({message:Object.assign(Object.assign({},c.message),l),notification:Object.assign(Object.assign({},c.notification),y)}),[l,y,c.message,c.notification]),[L,z]=(0,x.Z)(_.message),[T,V]=(0,g.Z)(_.notification),[U,F]=(0,n.Z)(),J=e.useMemo(()=>({message:L,notification:T,modal:U}),[L,T,U]);(0,m.ln)("App")(!(h&&r===!1),"usage","When using cssVar, ensure `component` is assigned a valid React component string.");const b=r===!1?e.Fragment:r,G={className:$,style:Z};return N(e.createElement(t.Z.Provider,{value:J},e.createElement(t.J.Provider,{value:_},e.createElement(b,Object.assign({},r===!1?void 0:G),F,z,V,A))))},B=()=>e.useContext(t.Z);const D=I;D.useApp=B;var R=D}}]);
