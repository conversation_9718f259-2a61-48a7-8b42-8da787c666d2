"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[3223],{20120:function(m,o){var e={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M832 112H724V72c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v40H500V72c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v40H320c-17.7 0-32 14.3-32 32v120h-96c-17.7 0-32 14.3-32 32v632c0 17.7 14.3 32 32 32h512c17.7 0 32-14.3 32-32v-96h96c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zM664 888H232V336h218v174c0 22.1 17.9 40 40 40h174v338zm0-402H514V336h.2L664 485.8v.2zm128 274h-56V456L544 264H360v-80h68v32c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-32h152v32c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-32h68v576z"}}]},name:"snippets",theme:"outlined"};o.Z=e},49173:function(m,o,e){e.r(o),e.d(o,{default:function(){return M}});var v=e(5574),p=e.n(v),g=e(89545),P=e(51477),h=e(66557),c=e(67294),C=e(20120),x=e(57079);function f(){return f=Object.assign?Object.assign.bind():function(d){for(var r=1;r<arguments.length;r++){var i=arguments[r];for(var s in i)Object.prototype.hasOwnProperty.call(i,s)&&(d[s]=i[s])}return d},f.apply(this,arguments)}const I=(d,r)=>c.createElement(x.Z,f({},d,{ref:r,icon:C.Z}));var T=c.forwardRef(I),B=e(82061),j=e(42509),O=e(11774),b=e(45360),R=e(83622),t=e(85893),Z=function(){var r=b.ZP.useMessage(),i=p()(r,2),s=i[0],A=i[1],u=(0,c.useRef)(),y=(0,c.useRef)(),H=function(n){return(0,t.jsx)("a",{onClick:function(){y.current.get(n.Id)},title:"\u7F16\u8F91",children:n.Name},"edit")},L=function(n){return(0,t.jsxs)("a",{onClick:function(){h.Z.post({message:"\u786E\u8BA4\u5408\u5E76\u5176\u4ED6\u540C\u540D\u7684\u652F\u4ED8\u65B9\u5F0F\u7684\u4EA4\u6613\u5230\u5230\u8FD9\u4E2A\u65B9\u5F0F\uFF0C\u5E76\u4E14\u5220\u9664\u5176\u4ED6\u540C\u540D\u7684\u652F\u4ED8\u65B9\u5F0F\uFF0C\u8BF7\u6CE8\u610F\uFF0C\u8BE5\u64CD\u4F5C\u4E0D\u53EF\u56DE\u9000",setPath:"/Pay/Base/PayType/combindPayType",data:{Id:n.Id},messageApi:s,onCallBack:function(){u.current.reload()}})},title:"\u5408\u5E76",children:[" ",(0,t.jsx)(T,{})]},"edit")},S=function(n){return(0,t.jsxs)("a",{onClick:function(){h.Z.post({message:"\u786E\u8BA4\u5220\u9664\u8BE5\u7B14\u4ED8\u6B3E\u65B9\u5F0F?",setPath:"/Pay/Base/PayType/deletePayType",data:{Id:n.Id},method:"DELETE",messageApi:s,onCallBack:function(){u.current.reload()}})},title:"\u5220\u9664",children:[" ",(0,t.jsx)(B.Z,{})]},"edit")},V=[{dataIndex:"RowIndex",title:"\u5E8F\u53F7",fixed:"left",align:"center"},{dataIndex:"Name",title:"\u7C7B\u578B\u540D\u79F0",align:"center",render:function(n,a){return H(a)}},{dataIndex:"CreateUserName",title:"\u521B\u5EFA\u4EBA",align:"center"},{dataIndex:"CreateTime",title:"\u521B\u5EFA\u65F6\u95F4",align:"center"},{align:"center",title:"\u5408\u5E76",fixed:"right",render:function(n,a){return L(a)}},{align:"center",title:"\u5220\u9664",fixed:"right",render:function(n,a){return S(a)}}],z=[(0,t.jsx)(R.ZP,{icon:(0,t.jsx)(j.Z,{}),style:{marginLeft:"10px"},type:"primary",onClick:function(){return y.current.get(void 0)},children:"\u6DFB\u52A0\u7C7B\u578B"},"cost-type-add")];return(0,t.jsxs)(O._z,{header:{breadcrumb:{},title:""},children:[A,(0,t.jsx)(P.Z,{ref:u,formCols:[],columns:V,tableButtons:z,getPath:"/Pay/Base/PayType/getPayTypeList",rowKey:"Id"},"cost-type-list-table"),(0,t.jsx)(g.default,{ref:y,formItems:[{name:"Id",type:"hidden",label:"",placeholder:"",required:!1},{name:"Name",type:"input",label:"\u4ED8\u6B3E\u65B9\u5F0F",placeholder:"\u8BF7\u8F93\u5165\u4ED8\u6B3E\u65B9\u5F0F\u540D\u79F0",required:!0,width:380},{name:"SortCode",type:"number",label:"\u663E\u793A\u6392\u5E8F",placeholder:"\u8BF7\u8F93\u5165\u663E\u793A\u6392\u5E8F",required:!0},{name:"ThirdPay",type:"switch",label:"\u4E09\u65B9\u652F\u4ED8",placeholder:"",required:!1,width:""},{name:"Remark",type:"textarea",label:"\u5907\u6CE8\u4FE1\u606F",placeholder:"\u8BF7\u8F93\u5165\u663E\u793A\u6392\u5E8F",required:!1,width:"100%",height:80}],modifyTitle:"\u4ED8\u6B3E\u65B9\u5F0F\u7F16\u8F91",insertTitle:"\u6DFB\u52A0\u4ED8\u6B3E\u65B9\u5F0F",initData:{Id:void 0,SortCode:9999},getPath:"/Pay/Base/PayType/getPayTypeInfo",setPath:"/Pay/Base/PayType/setPayTypeInfo",width:500,onCallBack:function(){u.current.reload()}})]})},M=Z}}]);
