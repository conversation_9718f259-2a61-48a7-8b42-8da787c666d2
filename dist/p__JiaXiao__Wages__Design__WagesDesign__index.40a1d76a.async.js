"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[8264],{65494:function(v,l,e){e.r(l);var i=e(5574),u=e.n(i),m=e(51477),g=e(66557),E=e(11774),I=e(45360),M=e(27484),r=e.n(M),c=e(67294),_=e(98765),n=e(85893),C=function(){var D=I.ZP.useMessage(),o=u()(D,2),P=o[0],f=o[1],s=(0,c.useRef)(),x=function(t){return(0,n.jsx)(_.Z,{SysId:t.SysId,Id:t.Id,onCallBack:function(){s.current.reload()}})},O=[{dataIndex:"RowIndex",title:"\u5E8F\u53F7",align:"center"},{dataIndex:"SysId",title:"\u7CFB\u7EDF\u7F16\u53F7",align:"center",render:function(t,a){return x(a)}},{dataIndex:"Remark",title:"\u5907\u6CE8",align:"center"},{dataIndex:"KeMuIdValue",title:"\u7ED3\u7B97\u79D1\u76EE",align:"center"},{dataIndex:"IsRecruitMySelfText",title:"\u81EA\u62DB\u751F",align:"center"},{dataIndex:"IsChangeCarTypeText",title:"\u6362\u8FC7\u8F66",align:"center"},{title:"\u62A5\u540D\u65E5\u671F",align:"center",render:function(t,a){return r()(a.RegistrationStartDate).format("YYYY/MM")+" ~ "+r()(a.RegistrationEndDate).format("YYYY/MM")}},{dataIndex:"CreateUserName",title:"\u521B\u5EFA\u4EBA",align:"center"},{dataIndex:"CreateTime",title:"\u521B\u5EFA\u65F6\u95F4",align:"center"},{dataIndex:"PriorityLevel",title:"\u4F18\u5148\u7EA7",align:"center"},{dataIndex:"Id",title:"\u5220\u9664",fixed:"left",align:"center",deleteColumn:!0,deletePath:"/Jx/Wages/Design/{id}",deleteMessage:"\u786E\u8BA4\u5220\u9664\u8BE5\u8BBE\u8BA1?"}],T=function(){g.Z.post({message:"\u662F\u5426\u786E\u8BA4\u4ECE\u4E2D\u5357\u5BFC\u5165\u8001\u7684\u6570\u636E?",setPath:"/Jx/Wages/OldData/importZhongNnaData",data:{},method:"PUT",messageApi:P,onCallBack:function(){s.current.reload()}})},B=[(0,n.jsx)(_.Z,{Id:void 0,style:{marginLeft:"10px"},onCallBack:function(){s.current.reload()}})];return(0,n.jsxs)(E._z,{header:{breadcrumb:{},title:""},children:[f,(0,n.jsx)(m.Z,{ref:s,formCols:[],columns:O,tableButtons:B,getPath:"/Jx/Wages/Design/getDesignList",rowKey:"Id"},"jx-area-list-table")]})};l.default=C}}]);
