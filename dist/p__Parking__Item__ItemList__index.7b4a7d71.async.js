"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[5278],{80305:function(W,a,s){s.r(a);var e=s(5574),m=s.n(e),x=s(67294),d=s(45360),p=s(36447),u=s(11774),t=s(85893),g=function(){var C=d.ZP.useMessage(),o=m()(C,2),O=o[0],j=o[1];return(0,t.jsxs)(t.Fragment,{children:[j,(0,t.jsx)(u._z,{header:{breadcrumb:{},title:""}})]})},n=function(){return(0,t.jsx)(p.Z,{children:(0,t.jsx)(g,{})})};a.default=n},36447:function(W,a,s){s.d(a,{Z:function(){return R}});var e=s(67294),m=s(93967),x=s.n(m),d=s(27288),p=s(53124),u=s(16474),t=s(94423),g=s(48311),n=s(66968),f=s(83559);const C=v=>{const{componentCls:i,colorText:A,fontSize:E,lineHeight:M,fontFamily:l}=v;return{[i]:{color:A,fontSize:E,lineHeight:M,fontFamily:l,[`&${i}-rtl`]:{direction:"rtl"}}}},o=()=>({});var O=(0,f.I$)("App",C,o),y=v=>{const{prefixCls:i,children:A,className:E,rootClassName:M,message:l,notification:h,style:Z,component:r="div"}=v,{direction:K,getPrefixCls:H}=(0,e.useContext)(p.E_),_=H("app",i),[N,S,I]=O(_),$=x()(S,_,E,M,I,{[`${_}-rtl`]:K==="rtl"}),c=(0,e.useContext)(n.J),P=e.useMemo(()=>({message:Object.assign(Object.assign({},c.message),l),notification:Object.assign(Object.assign({},c.notification),h)}),[l,h,c.message,c.notification]),[L,z]=(0,u.Z)(P.message),[T,V]=(0,g.Z)(P.notification),[U,b]=(0,t.Z)(),F=e.useMemo(()=>({message:L,notification:T,modal:U}),[L,T,U]);(0,d.ln)("App")(!(I&&r===!1),"usage","When using cssVar, ensure `component` is assigned a valid React component string.");const J=r===!1?e.Fragment:r,G={className:$,style:Z};return N(e.createElement(n.Z.Provider,{value:F},e.createElement(n.J.Provider,{value:P},e.createElement(J,Object.assign({},r===!1?void 0:G),b,z,V,A))))},B=()=>e.useContext(n.Z);const D=y;D.useApp=B;var R=D}}]);
