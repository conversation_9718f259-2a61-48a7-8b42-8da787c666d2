﻿import component from "@/locales/bn-BD/component";
import { keys } from "lodash";

/**
 * @name umi 的路由配置
 * @description 只支持 path,component,routes,redirect,wrappers,title 的配置
 * @param path  path 只支持两种占位符配置，第一种是动态参数 :id 的形式，第二种是 * 通配符，通配符只能出现路由字符串的最后。
 * @param component 配置 location 和 path 匹配后用于渲染的 React 组件路径。可以是绝对路径，也可以是相对路径，如果是相对路径，会从 src/pages 开始找起。
 * @param routes 配置子路由，通常在需要为多个路径增加 layout 组件时使用。
 * @param redirect 配置路由跳转
 * @param wrappers 配置路由组件的包装组件，通过包装组件可以为当前的路由组件组合进更多的功能。 比如，可以用于路由级别的权限校验
 * @doc https://umijs.org/docs/guides/routes
 */
export default [
    {
        path: '/user',
        layout: false,
        routes: [
            {
                name: 'login',
                path: '/user/login',
                component: './User/Login',
            },
        ],
    },
    {
        path: '/user/app',
        layout: false,
        routes: [
            {
                name: 'user/app',
                path: '/user/app',
                component: './User/App',
            },
        ],
    },
    {
        path: '/user/help',
        layout: false,
        component: './User/help',
        name: '帮助说明',
    },
    {
        path: '/Self/ExamSite/Cashier',
        layout: false,
        routes: [
            {
                name: 'Cashier',
                path: '/Self/ExamSite/Cashier',
                component: './Self/ExamSite/Cashier',
            },
        ],
    },
    {
        path: '/Self/ExamSite/Door',
        layout: false,
        routes: [
            {
                name: 'Door',
                path: '/Self/ExamSite/Door',
                component: './Self/ExamSite/Door',
            },
        ],
    },
    {
        path: '/Self/ExamSite/TouPiao',
        layout: false,
        routes: [
            {
                name: 'TouPiao',
                path: '/Self/ExamSite/TouPiao',
                component: './Self/ExamSite/TouPiao',
            },
        ],
    },
    {
        path: '/Self/H5/Cashier/Child/Pay',
        layout: false,
        routes: [
            {
                name: 'H5Cashier_Child',
                path: '/Self/H5/Cashier/Child/Pay',
                component: './Self/H5/Cashier/Child/Pay',
            },
        ],
    },
    {
        path: '/WeiXin/GetOpenId',
        layout: false,
        routes: [
            {
                name: 'GetOpenId',
                path: '/WeiXin/GetOpenId',
                component: './WeiXin/GetOpenId',
            },
        ],
    },
    {
        path: '/WeiXin/Subscribe',
        layout: false,
        routes: [
            {
                name: 'Subscribe',
                path: '/WeiXin/Subscribe',
                component: './WeiXin/Subscribe',
            },
        ],
    },
    {
        path: '/System/Tenant/TenantList',
        component: './System/Tenant/TenantList',
    },
    {
        path: '/System/Tenant/MyTenantList',
        component: './System/Tenant/MyTenantList',
    },
    {
        path: '/System/Menu/MenuList',
        component: './System/Menu/MenuList',
    },
    {
        path: '/System/Menu/MenuConfig',
        component: './System/Menu/MenuConfig',
    },
    {
        path: '/System/User/UserList',
        component: './System/User/UserList',
    },

    {
        path: '/System/User/CategoryList',
        component: './System/User/CategoryList',
    },

    {
        path: '/System/Role/RoleList',
        component: './System/Role/RoleList',
    },
    {
        path: '/System/UKeyList',
        component: './System/UKeyList',
        name: '数字证书管理',
    },
    {
        path: '/Wx/Config/ConfigList',
        component: './Wx/Config/ConfigList',
    },
    {
        path: '/JiaXiao/ExamSite/Field/FieldList',
        component: './JiaXiao/ExamSite/Field/FieldList',
    },
    {
        path: '/JiaXiao/ExamSite/Exam/ResultList',
        component: './JiaXiao/ExamSite/Exam/ResultList',
    },
    {
        path: '/JiaXiao/Student/StudentList',
        component: './JiaXiao/Student/StudentList',
    },
    {
        // path: '/JiaXiao/Student/StudentListX',
        // component: './JiaXiao/Student/StudentList',
    },
    {
        path: '/JiaXiao/Student/RegisterList',
        component: './JiaXiao/Student/RegisterList',
    },
    {
        path: '/JiaXiao/Student/ExamList',
        component: './JiaXiao/Student/ExamList',
    },
    {
        path: '/Pay/Account/AccountConfig',
        component: './Pay/Account/AccountConfig',
    },
    {
        path: '/CSJiaXie/Csjx/CoachExam',
        component: './CSJiaXie/Csjx/CoachExam',
    },
    {
        path: '/CSJiaXie/Csjx/CoachExamCount',
        component: './CSJiaXie/Csjx/CoachExamCount',
    },
    {
        path: '/CSJiaXie/Csjx/QuestionTitle',
        component: './CSJiaXie/Csjx/QuestionTitle',
    },
    {
        path: '/CSJiaXie/Csjx/QuestionSubTitle',
        component: './CSJiaXie/Csjx/QuestionSubTitle',
    },
    {
        path: '/CSJiaXie/Csjx/Question',
        component: './CSJiaXie/Csjx/Question',
    },
    {
        path: '/JiaXiao/Exam/Result/ResultList',
        component: './JiaXiao/Exam/Result/ResultList',
    },
    {
        path: '/JiaXiao/Exam/Police/ResultList',
        component: './JiaXiao/Exam/Police/ResultList',
    },
    {
        path: '/JiaXiao/ExamSite/Plan/PlanList',
        component: './JiaXiao/ExamSite/Plan/PlanList',
    },
    {
        path: '/JiaXiao/ExamSite/Plan/PlanDetailSpecialList',
        component: './JiaXiao/ExamSite/Plan/PlanDetailSpecialList',
    },
    {
        path: '/JiaXiao/ExamSite/Plan/PlanQueenList',
        component: './JiaXiao/ExamSite/Plan/PlanQueenList',
    },
    {
        path: '/JiaXiao/ExamSite/Train/TrainQueueList',
        component: './JiaXiao/ExamSite/Train/TrainQueueList',
    },
    {
        path: '/JiaXiao/ExamSite/Pay/CouponMoney',
        component: './JiaXiao/ExamSite/Pay/CouponMoney',
    },
    {
        path: '/JiaXiao/ExamSite/Pay/SaleList',
        component: './JiaXiao/ExamSite/Pay/SaleList',
    },
    {
        path: '/Base/Car/CarList',
        component: './Base/Car/CarList',
    },
    {
        path: '/Base/Car/Group/GroupList',
        component: './Base/Car/Group/GroupList',
    },
    {
        path: '/Base/Staff/StaffList',
        component: './Base/Staff/StaffList',
    },
    {
        path: '/JiaXiao/Base/JxArea/',
        component: './JiaXiao/Base/JxArea/',
    },
    {
        path: '/JiaXiao/Base/JxDept/',
        component: './JiaXiao/Base/JxDept/',
    },
    {
        path: '/JiaXiao/Base/JxField/',
        component: './JiaXiao/Base/JxField/',
    },
    {
        path: '/JiaXiao/Base/JxDeptAudit/',
        component: './JiaXiao/Base/JxDeptAudit/',
    },
    {
        path: '/JiaXiao/Base/JxFieldAudit/',
        component: './JiaXiao/Base/JxFieldAudit/',
    },
    {
        path: '/JiaXiao/Base/JxClass/',
        component: './JiaXiao/Base/JxClass/',
    },
    {
        path: '/JiaXiao/Base/JxCompany/',
        component: './JiaXiao/Base/JxCompany/',
    },
    {
        path: '/JiaXiao/Pay/CostType/',
        component: './JiaXiao/Pay/CostType/',
    },
    {
        path: '/Pay/Base/PayType/',
        component: './Pay/Base/PayType/',
    },
    {
        path: '/Pay/PayList/',
        component: './Pay/PayList/',
    },
    {
        path: '/JiaXiao/Student/StudentSale',
        component: './JiaXiao/Student/StudentSale',
    },
    {
        path: '/HnJiaXie/Company/CompanyList',
        component: './HnJiaXie/Company/CompanyList',
    },
    {
        path: '/HnJiaXie/Company/AddCompany',
        component: './HnJiaXie/Company/AddCompany',
    },
    {
        path: '/HnJiaXie/Coach/CoachList',
        component: './HnJiaXie/Coach/CoachList',
    },
    {
        path: '/HnJiaXie/Coach/ImageConfig',
        component: './HnJiaXie/Coach/ImageConfig',
    },
    {
        path: '/HnJiaXie/Question/QuestionList',
        component: './HnJiaXie/Question/QuestionList',
    },
    {
        path: '/HnJiaXie/Pay/PayList',
        component: './HnJiaXie/Pay/PayList',
    },
    {
        path: '/HnJiaXie/Pay/CostTypeList',
        component: './HnJiaXie/Pay/CostTypeList',
    },
    {
        path: '/HnJiaXie/CoachClass/CoachClassList',
        component: './HnJiaXie/CoachClass/CoachClassList',
    },
    {
        path: '/System/Config/Config',
        component: './System/Config/Config',
    },
    {
        path: '/JiaXiao/Pay/PayList',
        component: './JiaXiao/Pay/PayList',
    },
    {
        path: '/JiaXiao/Pay/AllPayList',
        component: './JiaXiao/Pay/PayList',
    },
    {
        path: '/JiaXiao/Pay/CostList',
        component: './JiaXiao/Pay/PayList',
    },
    {
        path: '/JiaXiao/Pay/ShouldPayList',
        component: './JiaXiao/Pay/ShouldPayList',
    },
    // {
    //     path: '/JiaXiao/Pay/MyPayList',
    //     component: './JiaXiao/Pay/MyPayList',
    // },
    // {
    //     path: '/JiaXiao/Pay/AllPayList',
    //     component: './JiaXiao/Pay/AllPayList',
    // },
    // {
    //     path: '/JiaXiao/Pay/MyPayList',
    //     component: './JiaXiao/Pay/MyPayList',
    // },
    {
        path: '/Pay/FuBeiDocList',
        component: './Pay/FuBeiDocList',
    },
    {
        path: '/Child/Child/Student/StudentList',
        component: './Child/Child/Student/StudentList',
    },
    {
        path: '/Child/Child/Class/ClassList',
        component: './Child/Child/Class/ClassList',
    },
    {
        path: '/Child/Child/Class/ClassList',
        component: './Child/Child/Class/ClassList',
    },
    {
        path: '/Child/Pay/AlreadPay/AlreadPayList',
        component: './Child/Pay/AlreadPay/AlreadPayList',
    },
    {
        path: '/Child/Pay/Template/TemplateList',
        component: './Child/Pay/Template/TemplateList',
    },
    {
        path: '/Child/Pay/Coupon/CouponList',
        component: './Child/Pay/Coupon/CouponList',
    },
    {
        path: '/Child/Pay/CostType/CostTypeList',
        component: './Child/Pay/CostType/CostTypeList',
    },
    // {
    //     path: '/Child/Pay/Student/StudentPayCount',
    //     component: './Child/Pay/Student/StudentPayCount',
    // },
    {
        path: '/Child/Child/Base/UserList',
        component: './Child/Child/Base/UserList',
    },
    {
        path: '/Pay/OrderList/',
        component: './Pay/OrderList/',
    },
    {
        path: '/Pay/OrderList/',
        component: './Pay/OrderList/',
    },
    {
        path: '/Child/ShouldPay/ShouldPayList',
        component: './Child/ShouldPay/ShouldPayList',
    },
    {
        path: '/JiaXiao/Contract/ContractTemplateList',
        component: './JiaXiao/Contract/ContractTemplateList',
    },
    {
        path: '/System/Third/ThirdConfig',
        component: './System/Third/ThirdConfig',
    },
    {
        path: '/HnJiaXie/Coach/MaxId',
        component: './HnJiaXie/Coach/MaxId',
    },
    {
        path: '/System/ExcelDesign/ExcelDesignConfig',
        component: './System/ExcelDesign/ExcelDesignConfig',
    },
    {
        path: '/System/PageDesign/PageDesignConfig',
        component: './System/PageDesign/PageDesignConfig',
    },
    {
        path: '/System/DB/DBList',
        component: './System/DB/DBList',
    },
    {
        path: '/System/Config/SystemConfig',
        component: './System/Config/SystemConfig',
    },
    {
        path: '/JiaXiao/Study/CarList',
        component: './JiaXiao/Study/CarList',
    },
    {
        path: '/JiaXiao/Study/StudyList',
        component: './JiaXiao/Study/StudyList',
    },
    {
        path: '/Parking/Place/PlaceList',
        component: './Parking/Place/PlaceList',
    },
    {
        path: '/Parking/Pay/PayList',
        component: './Parking/Pay/PayList',
    },
    {
        path: '/Parking/Log/LogList',
        component: './Parking/Log/LogList',
    },
    {
        path: '/Parking/Item/ItemList',
        component: './Parking/Item/ItemList',
    },
    {
        path: '/JiaXiao/Student/DocStatusList',
        component: './JiaXiao/Student/DocStatusList',
    },
    {
        path: '/JiaXiao/Student/AssignCoachList',
        component: './JiaXiao/Student/AssignCoachList',
    },
    {
        path: '/JiaXiao/Student/Face/FaceLogList',
        component: './JiaXiao/Student/Face/FaceLogList',
    },
    {
        path: '/JiaXiao/Base/JxDevice',
        component: './JiaXiao/Base/JxDevice',
    },

    {
        path: '/JiaXiao/ExamSite/Pay/CouponPoint',
        component: './JiaXiao/ExamSite/Pay/CouponPoint',
    },
    {
        path: '/JiaXiao/OrderCar/RankClass',
        component: './JiaXiao/OrderCar/RankClass',
    },
    {
        path: '/JiaXiao/OrderCar/RankClassSwitch',
        component: './JiaXiao/OrderCar/RankClassSwitch',
    },
    {
        path: '/JiaXiao/OrderCar/OrderCarConfig',
        component: './JiaXiao/OrderCar/OrderCarConfig',
    },
    {
        path: '/JiaXiao/OrderCar/Record',
        component: './JiaXiao/OrderCar/Record',
    },
    {
        path: '/JiaXiao/Study/SupervisionPlatform/SupRegionList',
        component: './JiaXiao/Study/SupervisionPlatform/SupRegionList',
    },
    {
        path: '/JiaXiao/Pay/PushMoneyMethod',
        component: './JiaXiao/Pay/PushMoneyMethod',
    },
    {
        path: '/JiaXiao/Student/StudentCount',
        component: './JiaXiao/Student/StudentCount',
    },
    {
        path: '/JiaXiao/Exam/Police/UpdateExamLog',
        component: './JiaXiao/Exam/Police/UpdateExamLog',
    },
    {
        path: '/Pay/Base/Account',
        component: './Pay/Base/Account',
    },
    {
        path: '/JiaXiao/Shop/ItemList',
        component: './JiaXiao/Shop/ItemList',
    },
    {
        path: '/JiaXiao/Shop/KitList',
        component: './JiaXiao/Shop/KitList',
    },
    {
        path: '/JiaXiao/Pay/PushMoneyList',
        component: './JiaXiao/Pay/PushMoneyList',
    },
    {
        path: '/JiaXiao/Student/AssignCoachList',
        component: './JiaXiao/Student/AssignCoachList',
    },
    {
        path: '/JiaXiao/Student/MyStudentColumnList',
        component: './JiaXiao/Student/MyStudentColumnList',
    },
    {
        path: '/JiaXiao/Student/ImformationStatusList',
        component: './JiaXiao/Student/ImformationStatusList',
    },
    {
        path: '/JiaXiao/Student/SourceList',
        component: './JiaXiao/Student/SourceList',
    },
    {
        path: '/JiaXiao/Exam/Result/ResultCount',
        component: './JiaXiao/Exam/Result/ResultCount',
    },
    {
        path: '/JiaXiao/Wages/Design/WagesDesign',
        component: './JiaXiao/Wages/Design/WagesDesign',
    },
    {
        path: '/JiaXiao/Wages/Result/WageResult',
        component: './JiaXiao/Wages/Result/WageResult',
    },
    {
        path: '/System/Log/LogList',
        component: './System/Log/LogList',
    },
    {
        path: '/Wx/Config//MakeMenu',
        component: './Wx/Config//MakeMenu',
    },
    {
        path: '/JiaXiao/ExamSite/Car/LogList',
        component: './JiaXiao/ExamSite/Car/LogList',
    },
    {
        path: '/JiaXiao/Base/UKey/UKeyList',
        component: './JiaXiao/Base/UKey/UKeyList',
    },
    {
        path: '/Pay/JlPay/Test',
        component: './Pay/JlPay/Test',
    },
    {
        path: '/System/User/CheckInList',
        component: './System/User/CheckInList',
    },
    {
        path: '/JiaXiao/ExamSite/Study/StudyCount',
        component: './JiaXiao/ExamSite/Study/StudyCount',
    },
    {
        path: '/JiaXiao/ExamSite/Study/StudyList',
        component: './JiaXiao/ExamSite/Study/StudyList'
    },
    {
        path: '/JiaXiao/ExamSite/Plan/OpenPlanDetail',
        component: './JiaXiao/ExamSite/Plan/OpenPlanDetail'
    },
    {
        path: '/JiaXiao/ExamSite/Plan/OpenOrderPlanDetail',
        component: './JiaXiao/ExamSite/Plan/OpenOrderPlanDetail'
    },
    {
        path: '/JiaXiao/Student/Audit/StudentModifyReview',
        component: './JiaXiao/Student/Audit/StudentModifyReview'
    },
    {
        path: '/JiaXiao/Student/Audit/StudentDiscountReview',
        component: './JiaXiao/Student/Audit/StudentDiscountReview'
    },
    {
        path: '/JiaXiao/Student/Audit/StudentStatusChangeReview',
        component: './JiaXiao/Student/Audit/StudentStatusChangeReview'
    },
    {
        path: '/JiaXiao/Student/Audit/RefundAudit',
        component: './JiaXiao/Student/Audit/RefundAudit'
    },
    {
        path: '/JiaXiao/Student/Audit/WxRefundAudit',
        component: './JiaXiao/Student/Audit/WxRefundAudit',
        name: '微信退款审核'
    },
    // {
    //     path: '/',
    //     redirect: '/welcome',
    // },
    {
        path: '*',
        component: './Welcome',
    },
    {
        path: '/JiaXiao/ExamSite/Study/StudyList',
        component: './JiaXiao/ExamSite/Study/StudyList'
    },
    {
        path: '/JiaXiao/ExamSite/Audit/RefundAudit',
        component: './JiaXiao/ExamSite/Audit/RefundAudit',
    },
    {
        path: '/JiaXiao/ExamSite/Plan/OpenPlanDetail',
        component: './JiaXiao/ExamSite/Plan/OpenPlanDetail'
    },
    {
        path: '/JiaXiao/SMS/Log',
        component: './JiaXiao/SMS/Log',
    },
    {
        path: '/JiaXiao/SMS/Config',
        component: './JiaXiao/SMS/Config',
        name: '短信配置',
    },
    {
        path: '/wx',
        name: '微信管理',
        routes: [
            {
                path: '/wx/media',
                name: '素材管理',
                component: './Wx/Media',
            },
            // ... 其他微信相关路由 ...
        ],
    },
    {
        path: '/TestPost',
        name: 'API测试',
        icon: 'ApiOutlined',
        component: './TestPost',
    },
    {
        path: '/TheoreticalExam/TheoreticalExamList',
        component: './TheoreticalExam/TheoreticalExamList',
    },
    {
        path: '/JiaXiao/ExamSite/Pay/AccountBilling',
        component: './JiaXiao/ExamSite/Pay/AccountBilling',
        name: '驾校对账',
    },
    {
        path: '/JiaXiao/OrderCar/OrderCar',
        component: './JiaXiao/OrderCar/OrderCar',
        name: '约车管理',
    },
    {
        path: '/JiaXiao/Invoice/InvoiceList',
        component: './JiaXiao/Invoice/InvoiceList',
        name: '发票管理',
    },
];
